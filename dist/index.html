<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>RealHonest – get real, be real</title>
  <meta name="description" content="A platform for honest conversations among visionaries" />

  <!-- Performance optimizations -->
  <link rel="preconnect" href="https://numbersapi.com">
  <link rel="dns-prefetch" href="https://numbersapi.com">
  <meta name="theme-color" content="#6366f1" />

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- Icons -->
  <link rel="icon" href="/assets/favicon-B_SY1GJM.ico" type="image/x-icon" />
  <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💬</text></svg>" />
  <link rel="apple-touch-icon" href="/static/icon-192.png">

  <!-- Apple PWA meta tags -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="RealHonest">

  <!-- Critical CSS for faster rendering -->
  <style>
    body{font-family:system-ui,sans-serif;background-color:#f9fafb;color:#111827;transition:background-color .3s ease,color .3s ease}
    .dark{background-color:#111827;color:#f9fafb}
    .dark body{background-color:#111827;color:#f9fafb}
    .bg-white{background-color:#fff}
    .dark .bg-white{background-color:#1f2937!important}
    .text-gray-900{color:#111827}
    .dark .text-gray-900{color:#f9fafb!important}
    .transition-colors{transition-property:color,background-color,border-color;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}
    #root{min-height:100vh;display:flex;flex-direction:column}
  </style>
  <script type="module" crossorigin src="/assets/index-BGrGIm-9.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/vendor-DXBhkOeJ.js">
  <link rel="modulepreload" crossorigin href="/assets/router-xL491rWF.js">
  <link rel="modulepreload" crossorigin href="/assets/quill-C38wxQcs.js">
  <link rel="stylesheet" crossorigin href="/assets/index-DGYDQReb.css">
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300">
  <div id="root"></div>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/service-worker.js')
          .then(function(registration) {
            console.log('SW registered: ', registration);
          })
          .catch(function(registrationError) {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
</body>
</html>