import{r as u,R as oe}from"./vendor-DXBhkOeJ.js";/**
 * @remix-run/router v1.17.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},B.apply(this,arguments)}var w;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(w||(w={}));const V="popstate";function se(e){e===void 0&&(e={});function t(r,a){let{pathname:l,search:i,hash:s}=r.location;return $("",{pathname:l,search:i,hash:s},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:N(a)}return ce(t,n,null,e)}function g(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function X(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ue(){return Math.random().toString(36).substr(2,8)}function D(e,t){return{usr:e.state,key:e.key,idx:t}}function $(e,t,n,r){return n===void 0&&(n=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?U(t):t,{state:n,key:t&&t.key||r||ue()})}function N(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function U(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function ce(e,t,n,r){r===void 0&&(r={});let{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,s=w.Pop,o=null,f=h();f==null&&(f=0,i.replaceState(B({},i.state,{idx:f}),""));function h(){return(i.state||{idx:null}).idx}function c(){s=w.Pop;let d=h(),x=d==null?null:d-f;f=d,o&&o({action:s,location:m.location,delta:x})}function p(d,x){s=w.Push;let E=$(m.location,d,x);f=h()+1;let C=D(E,f),R=m.createHref(E);try{i.pushState(C,"",R)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;a.location.assign(R)}l&&o&&o({action:s,location:m.location,delta:1})}function y(d,x){s=w.Replace;let E=$(m.location,d,x);f=h();let C=D(E,f),R=m.createHref(E);i.replaceState(C,"",R),l&&o&&o({action:s,location:m.location,delta:0})}function v(d){let x=a.location.origin!=="null"?a.location.origin:a.location.href,E=typeof d=="string"?d:N(d);return E=E.replace(/ $/,"%20"),g(x,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,x)}let m={get action(){return s},get location(){return e(a,i)},listen(d){if(o)throw new Error("A history only accepts one active listener");return a.addEventListener(V,c),o=d,()=>{a.removeEventListener(V,c),o=null}},createHref(d){return t(a,d)},createURL:v,encodeLocation(d){let x=v(d);return{pathname:x.pathname,search:x.search,hash:x.hash}},push:p,replace:y,go(d){return i.go(d)}};return m}var z;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(z||(z={}));function fe(e,t,n){return n===void 0&&(n="/"),he(e,t,n)}function he(e,t,n,r){let a=typeof t=="string"?U(t):t,l=M(a.pathname||"/",n);if(l==null)return null;let i=Q(e);de(i);let s=null;for(let o=0;s==null&&o<i.length;++o){let f=be(l);s=Re(i[o],f)}return s}function Q(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let a=(l,i,s)=>{let o={relativePath:s===void 0?l.path||"":s,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};o.relativePath.startsWith("/")&&(g(o.relativePath.startsWith(r),'Absolute route path "'+o.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),o.relativePath=o.relativePath.slice(r.length));let f=P([r,o.relativePath]),h=n.concat(o);l.children&&l.children.length>0&&(g(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+f+'".')),Q(l.children,t,h,f)),!(l.path==null&&!l.index)&&t.push({path:f,score:Ce(f,l.index),routesMeta:h})};return e.forEach((l,i)=>{var s;if(l.path===""||!((s=l.path)!=null&&s.includes("?")))a(l,i);else for(let o of Y(l.path))a(l,i,o)}),t}function Y(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let i=Y(r.join("/")),s=[];return s.push(...i.map(o=>o===""?l:[l,o].join("/"))),a&&s.push(...i),s.map(o=>e.startsWith("/")&&o===""?"/":o)}function de(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Ee(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const pe=/^:[\w-]+$/,me=3,ve=2,ge=1,ye=10,xe=-2,J=e=>e==="*";function Ce(e,t){let n=e.split("/"),r=n.length;return n.some(J)&&(r+=xe),t&&(r+=ve),n.filter(a=>!J(a)).reduce((a,l)=>a+(pe.test(l)?me:l===""?ge:ye),r)}function Ee(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function Re(e,t,n){let{routesMeta:r}=e,a={},l="/",i=[];for(let s=0;s<r.length;++s){let o=r[s],f=s===r.length-1,h=l==="/"?t:t.slice(l.length)||"/",c=we({path:o.relativePath,caseSensitive:o.caseSensitive,end:f},h),p=o.route;if(!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:P([l,c.pathname]),pathnameBase:Be(P([l,c.pathnameBase])),route:p}),c.pathnameBase!=="/"&&(l=P([l,c.pathnameBase]))}return i}function we(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=Pe(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce((f,h,c)=>{let{paramName:p,isOptional:y}=h;if(p==="*"){let m=s[c]||"";i=l.slice(0,l.length-m.length).replace(/(.)\/+$/,"$1")}const v=s[c];return y&&!v?f[p]=void 0:f[p]=(v||"").replace(/%2F/g,"/"),f},{}),pathname:l,pathnameBase:i,pattern:e}}function Pe(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),X(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,s,o)=>(r.push({paramName:s,isOptional:o!=null}),o?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function be(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return X(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function M(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Se(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?U(e):e;return{pathname:n?n.startsWith("/")?n:Le(n,t):t,search:Oe(r),hash:Ie(a)}}function Le(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function k(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Ue(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Z(e,t){let n=Ue(e);return t?n.map((r,a)=>a===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function H(e,t,n,r){r===void 0&&(r=!1);let a;typeof e=="string"?a=U(e):(a=B({},e),g(!a.pathname||!a.pathname.includes("?"),k("?","pathname","search",a)),g(!a.pathname||!a.pathname.includes("#"),k("#","pathname","hash",a)),g(!a.search||!a.search.includes("#"),k("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,s;if(i==null)s=n;else{let c=t.length-1;if(!r&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),c-=1;a.pathname=p.join("/")}s=c>=0?t[c]:"/"}let o=Se(a,s),f=i&&i!=="/"&&i.endsWith("/"),h=(l||i===".")&&n.endsWith("/");return!o.pathname.endsWith("/")&&(f||h)&&(o.pathname+="/"),o}const P=e=>e.join("/").replace(/\/\/+/g,"/"),Be=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Oe=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ie=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Ne(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ee=["post","put","patch","delete"];new Set(ee);const Te=["get",...ee];new Set(Te);/**
 * React Router v6.24.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function O(){return O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},O.apply(this,arguments)}const W=u.createContext(null),je=u.createContext(null),S=u.createContext(null),T=u.createContext(null),b=u.createContext({outlet:null,matches:[],isDataRoute:!1}),te=u.createContext(null);function ke(e,t){let{relative:n}=t===void 0?{}:t;I()||g(!1);let{basename:r,navigator:a}=u.useContext(S),{hash:l,pathname:i,search:s}=re(e,{relative:n}),o=i;return r!=="/"&&(o=i==="/"?r:P([r,i])),a.createHref({pathname:o,search:s,hash:l})}function I(){return u.useContext(T)!=null}function j(){return I()||g(!1),u.useContext(T).location}function ne(e){u.useContext(S).static||u.useLayoutEffect(e)}function $e(){let{isDataRoute:e}=u.useContext(b);return e?Xe():Fe()}function Fe(){I()||g(!1);let e=u.useContext(W),{basename:t,future:n,navigator:r}=u.useContext(S),{matches:a}=u.useContext(b),{pathname:l}=j(),i=JSON.stringify(Z(a,n.v7_relativeSplatPath)),s=u.useRef(!1);return ne(()=>{s.current=!0}),u.useCallback(function(f,h){if(h===void 0&&(h={}),!s.current)return;if(typeof f=="number"){r.go(f);return}let c=H(f,JSON.parse(i),l,h.relative==="path");e==null&&t!=="/"&&(c.pathname=c.pathname==="/"?t:P([t,c.pathname])),(h.replace?r.replace:r.push)(c,h.state,h)},[t,r,i,l,e])}function ut(){let{matches:e}=u.useContext(b),t=e[e.length-1];return t?t.params:{}}function re(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=u.useContext(S),{matches:a}=u.useContext(b),{pathname:l}=j(),i=JSON.stringify(Z(a,r.v7_relativeSplatPath));return u.useMemo(()=>H(e,JSON.parse(i),l,n==="path"),[e,i,l,n])}function _e(e,t){return Me(e,t)}function Me(e,t,n,r){I()||g(!1);let{navigator:a}=u.useContext(S),{matches:l}=u.useContext(b),i=l[l.length-1],s=i?i.params:{};i&&i.pathname;let o=i?i.pathnameBase:"/";i&&i.route;let f=j(),h;if(t){var c;let d=typeof t=="string"?U(t):t;o==="/"||(c=d.pathname)!=null&&c.startsWith(o)||g(!1),h=d}else h=f;let p=h.pathname||"/",y=p;if(o!=="/"){let d=o.replace(/^\//,"").split("/");y="/"+p.replace(/^\//,"").split("/").slice(d.length).join("/")}let v=fe(e,{pathname:y}),m=Je(v&&v.map(d=>Object.assign({},d,{params:Object.assign({},s,d.params),pathname:P([o,a.encodeLocation?a.encodeLocation(d.pathname).pathname:d.pathname]),pathnameBase:d.pathnameBase==="/"?o:P([o,a.encodeLocation?a.encodeLocation(d.pathnameBase).pathname:d.pathnameBase])})),l,n,r);return t&&m?u.createElement(T.Provider,{value:{location:O({pathname:"/",search:"",hash:"",state:null,key:"default"},h),navigationType:w.Pop}},m):m}function We(){let e=Ge(),t=Ne(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return u.createElement(u.Fragment,null,u.createElement("h2",null,"Unexpected Application Error!"),u.createElement("h3",{style:{fontStyle:"italic"}},t),n?u.createElement("pre",{style:a},n):null,null)}const Ve=u.createElement(We,null);class De extends u.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?u.createElement(b.Provider,{value:this.props.routeContext},u.createElement(te.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ze(e){let{routeContext:t,match:n,children:r}=e,a=u.useContext(W);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),u.createElement(b.Provider,{value:t},r)}function Je(e,t,n,r){var a;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var l;if((l=n)!=null&&l.errors)e=n.matches;else return null}let i=e,s=(a=n)==null?void 0:a.errors;if(s!=null){let h=i.findIndex(c=>c.route.id&&(s==null?void 0:s[c.route.id])!==void 0);h>=0||g(!1),i=i.slice(0,Math.min(i.length,h+1))}let o=!1,f=-1;if(n&&r&&r.v7_partialHydration)for(let h=0;h<i.length;h++){let c=i[h];if((c.route.HydrateFallback||c.route.hydrateFallbackElement)&&(f=h),c.route.id){let{loaderData:p,errors:y}=n,v=c.route.loader&&p[c.route.id]===void 0&&(!y||y[c.route.id]===void 0);if(c.route.lazy||v){o=!0,f>=0?i=i.slice(0,f+1):i=[i[0]];break}}}return i.reduceRight((h,c,p)=>{let y,v=!1,m=null,d=null;n&&(y=s&&c.route.id?s[c.route.id]:void 0,m=c.route.errorElement||Ve,o&&(f<0&&p===0?(Qe("route-fallback"),v=!0,d=null):f===p&&(v=!0,d=c.route.hydrateFallbackElement||null)));let x=t.concat(i.slice(0,p+1)),E=()=>{let C;return y?C=m:v?C=d:c.route.Component?C=u.createElement(c.route.Component,null):c.route.element?C=c.route.element:C=h,u.createElement(ze,{match:c,routeContext:{outlet:h,matches:x,isDataRoute:n!=null},children:C})};return n&&(c.route.ErrorBoundary||c.route.errorElement||p===0)?u.createElement(De,{location:n.location,revalidation:n.revalidation,component:m,error:y,children:E(),routeContext:{outlet:null,matches:x,isDataRoute:!0}}):E()},null)}var ae=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ae||{}),le=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(le||{});function Ae(e){let t=u.useContext(W);return t||g(!1),t}function Ke(e){let t=u.useContext(je);return t||g(!1),t}function qe(e){let t=u.useContext(b);return t||g(!1),t}function ie(e){let t=qe(),n=t.matches[t.matches.length-1];return n.route.id||g(!1),n.route.id}function Ge(){var e;let t=u.useContext(te),n=Ke(),r=ie();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Xe(){let{router:e}=Ae(ae.UseNavigateStable),t=ie(le.UseNavigateStable),n=u.useRef(!1);return ne(()=>{n.current=!0}),u.useCallback(function(a,l){l===void 0&&(l={}),n.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,O({fromRouteId:t},l)))},[e,t])}const A={};function Qe(e,t,n){A[e]||(A[e]=!0)}function Ye(e){g(!1)}function Ze(e){let{basename:t="/",children:n=null,location:r,navigationType:a=w.Pop,navigator:l,static:i=!1,future:s}=e;I()&&g(!1);let o=t.replace(/^\/*/,"/"),f=u.useMemo(()=>({basename:o,navigator:l,static:i,future:O({v7_relativeSplatPath:!1},s)}),[o,s,l,i]);typeof r=="string"&&(r=U(r));let{pathname:h="/",search:c="",hash:p="",state:y=null,key:v="default"}=r,m=u.useMemo(()=>{let d=M(h,o);return d==null?null:{location:{pathname:d,search:c,hash:p,state:y,key:v},navigationType:a}},[o,h,c,p,y,v,a]);return m==null?null:u.createElement(S.Provider,{value:f},u.createElement(T.Provider,{children:n,value:m}))}function ct(e){let{children:t,location:n}=e;return _e(F(t),n)}new Promise(()=>{});function F(e,t){t===void 0&&(t=[]);let n=[];return u.Children.forEach(e,(r,a)=>{if(!u.isValidElement(r))return;let l=[...t,a];if(r.type===u.Fragment){n.push.apply(n,F(r.props.children,l));return}r.type!==Ye&&g(!1),!r.props.index||!r.props.children||g(!1);let i={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=F(r.props.children,l)),n.push(i)}),n}/**
 * React Router DOM v6.24.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _(){return _=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_.apply(this,arguments)}function He(e,t){if(e==null)return{};var n={},r=Object.keys(e),a,l;for(l=0;l<r.length;l++)a=r[l],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}function et(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function tt(e,t){return e.button===0&&(!t||t==="_self")&&!et(e)}const nt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","unstable_viewTransition"],rt="6";try{window.__reactRouterVersion=rt}catch{}const at="startTransition",K=oe[at];function ft(e){let{basename:t,children:n,future:r,window:a}=e,l=u.useRef();l.current==null&&(l.current=se({window:a,v5Compat:!0}));let i=l.current,[s,o]=u.useState({action:i.action,location:i.location}),{v7_startTransition:f}=r||{},h=u.useCallback(c=>{f&&K?K(()=>o(c)):o(c)},[o,f]);return u.useLayoutEffect(()=>i.listen(h),[i,h]),u.createElement(Ze,{basename:t,children:n,location:s.location,navigationType:s.action,navigator:i,future:r})}const lt=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",it=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ht=u.forwardRef(function(t,n){let{onClick:r,relative:a,reloadDocument:l,replace:i,state:s,target:o,to:f,preventScrollReset:h,unstable_viewTransition:c}=t,p=He(t,nt),{basename:y}=u.useContext(S),v,m=!1;if(typeof f=="string"&&it.test(f)&&(v=f,lt))try{let C=new URL(window.location.href),R=f.startsWith("//")?new URL(C.protocol+f):new URL(f),L=M(R.pathname,y);R.origin===C.origin&&L!=null?f=L+R.search+R.hash:m=!0}catch{}let d=ke(f,{relative:a}),x=ot(f,{replace:i,state:s,target:o,preventScrollReset:h,relative:a,unstable_viewTransition:c});function E(C){r&&r(C),C.defaultPrevented||x(C)}return u.createElement("a",_({},p,{href:v||d,onClick:m||l?r:E,ref:n,target:o}))});var q;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(q||(q={}));var G;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(G||(G={}));function ot(e,t){let{target:n,replace:r,state:a,preventScrollReset:l,relative:i,unstable_viewTransition:s}=t===void 0?{}:t,o=$e(),f=j(),h=re(e,{relative:i});return u.useCallback(c=>{if(tt(c,n)){c.preventDefault();let p=r!==void 0?r:N(f)===N(h);o(e,{replace:p,state:a,preventScrollReset:l,relative:i,unstable_viewTransition:s})}},[f,o,h,r,a,n,e,l,i,s])}export{ft as B,ht as L,ct as R,ut as a,Ye as b,$e as u};
