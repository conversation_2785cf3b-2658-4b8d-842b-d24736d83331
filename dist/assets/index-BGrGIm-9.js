import{r as s,a as G,b as M}from"./vendor-DXBhkOeJ.js";import{L as q,u as I,a as A,R as J,b as E,B as Q}from"./router-xL491rWF.js";import{R as W}from"./quill-C38wxQcs.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))u(d);new MutationObserver(d=>{for(const i of d)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&u(o)}).observe(document,{childList:!0,subtree:!0});function r(d){const i={};return d.integrity&&(i.integrity=d.integrity),d.referrerPolicy&&(i.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?i.credentials="include":d.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function u(d){if(d.ep)return;d.ep=!0;const i=r(d);fetch(d.href,i)}})();var Y={exports:{}},O={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X=s,K=Symbol.for("react.element"),Z=Symbol.for("react.fragment"),ee=Object.prototype.hasOwnProperty,te=X.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,se={key:!0,ref:!0,__self:!0,__source:!0};function z(t,a,r){var u,d={},i=null,o=null;r!==void 0&&(i=""+r),a.key!==void 0&&(i=""+a.key),a.ref!==void 0&&(o=a.ref);for(u in a)ee.call(a,u)&&!se.hasOwnProperty(u)&&(d[u]=a[u]);if(t&&t.defaultProps)for(u in a=t.defaultProps,a)d[u]===void 0&&(d[u]=a[u]);return{$$typeof:K,type:t,key:i,ref:o,props:d,_owner:te.current}}O.Fragment=Z;O.jsx=z;O.jsxs=z;Y.exports=O;var e=Y.exports,U={},B=G;U.createRoot=B.createRoot,U.hydrateRoot=B.hydrateRoot;const ae={position:"absolute",left:"-9999px",top:"auto",width:"1px",height:"1px",overflow:"hidden"},re=M.createContext(),ne=({children:t})=>{const[a,r]=s.useState("");return e.jsxs(re.Provider,{value:{announce:r},children:[e.jsx("div",{"aria-live":"polite",style:ae,children:a}),t]})};function oe({trigger:t,children:a,className:r="right-0 mt-2 w-48"}){const[u,d]=M.useState(!1),i=l=>{l.preventDefault(),l.stopPropagation(),d(h=>!h)};M.useEffect(()=>{const l=()=>d(!1);return window.addEventListener("click",l),()=>window.removeEventListener("click",l)},[]);const o=l=>{l.stopPropagation()};return e.jsxs("div",{className:"relative inline-block text-left profile-menu-container",onClick:i,children:[t,u&&e.jsx("div",{className:`absolute ${r} rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-50 focus:outline-none z-50`,role:"menu","aria-orientation":"vertical",tabIndex:"-1",onClick:o,children:e.jsx("div",{className:"py-1",role:"none",children:a})})]})}function ie(){return e.jsx("a",{href:"#main-content",className:"sr-only focus:not(.sr-only)",children:e.jsx("span",{className:"fixed top-0 left-0 m-4 p-3 bg-indigo-600 text-white rounded z-50",children:"Skip to content"})})}function P({user:t,children:a}){var d;const[r,u]=M.useState(!1);return M.useEffect(()=>{(async()=>{let o=!1;if(t)try{const l=await fetch("/api/user/dark-mode",{credentials:"include"});l.ok&&(o=(await l.json()).dark_mode)}catch{console.log("Could not fetch dark mode preference from server")}if(!t){const l=localStorage.getItem("darkMode");o=l==="true"||l===null&&window.matchMedia("(prefers-color-scheme: dark)").matches}u(o)})()},[t]),M.useEffect(()=>{r?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode",r?"true":"false"),t&&fetch("/api/user/dark-mode",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({dark_mode:r})}).catch(i=>{console.log("Could not save dark mode preference to server:",i)})},[r,t]),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300",children:[e.jsx(ie,{}),e.jsxs("header",{className:"bg-white dark:bg-gray-800 shadow-sm p-4 flex justify-between items-center transition-colors duration-300",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"w-6 h-6 text-indigo-600",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:e.jsx("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"})}),e.jsx("h1",{className:"text-xl font-bold",children:"RealHonest – get real, be real"})]}),e.jsxs("nav",{className:"space-x-4 hidden md:flex items-center",children:[e.jsx("a",{href:"/",className:"hover:text-indigo-600 transition",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"hover:text-indigo-600 transition",children:"Dashboard"}),e.jsx("a",{href:"/mentors",className:"hover:text-indigo-600 transition",children:"Mentors"}),t&&e.jsxs(oe,{trigger:e.jsxs("button",{type:"button","aria-label":"User menu",className:"focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full ml-4",children:[t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?e.jsx("img",{src:t.avatar_url,alt:"User avatar",className:"w-8 h-8 rounded-full border-2 border-indigo-500 hover:border-indigo-700 transition object-cover",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="flex"}}):null,e.jsx("div",{className:"w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-semibold border-2 border-indigo-500 hover:border-indigo-700 transition",style:{display:t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?"none":"flex"},children:((d=t.username)==null?void 0:d.charAt(0).toUpperCase())||"?"})]}),className:"right-0 mt-2 w-48 z-50",children:[e.jsx(q,{to:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"🧾 Settings"}),e.jsx(q,{to:"/archived-chats",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📁 Archived Chats"}),e.jsx(q,{to:"/download",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📲 Download App"}),e.jsx("button",{type:"button",className:"w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-900",onClick:async()=>{if(window.confirm("Are you sure you want to delete your account?"))try{(await fetch("/api/user",{method:"DELETE",credentials:"include"})).ok?window.location.href="/logout":alert("Failed to delete account")}catch{alert("Error deleting account")}},children:"❌ Delete Account"})]}),e.jsx("button",{onClick:()=>u(!r),"aria-label":r?"Switch to light mode":"Switch to dark mode",className:"ml-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 rounded-md px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:r?"🌞 Light Mode":"🌙 Dark Mode"}),e.jsx("a",{href:"/login",className:"ml-4 hover:text-red-600 transition",children:"🚪 Login"}),e.jsx("a",{href:"/logout",className:"ml-4 hover:text-red-600 transition",children:"🚪 Logout"})]})]}),e.jsxs("main",{className:"flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6",children:[t&&e.jsx("aside",{className:"md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sticky top-6 transition-colors duration-300",children:e.jsxs("nav",{className:"space-y-2",children:[e.jsx("a",{href:"/",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📊 Dashboard"}),e.jsx("a",{href:"/saved-posts",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🔖 Saved Posts"}),e.jsx("a",{href:"/activity",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📈 Activity"}),e.jsx("a",{href:"/profile",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"⚙️ Profile"}),e.jsx("a",{href:"/chat",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"💬 Chat"}),e.jsx("a",{href:"/logout",className:"block py-2 px-3 hover:bg-red-100 dark:hover:bg-red-900 rounded text-red-600 dark:text-red-400 transition-colors",children:"🚪 Logout"})]})}),e.jsx("section",{id:"main-content",className:"flex-grow",children:a})]}),e.jsxs("footer",{className:"py-6 px-4 text-center text-gray-500 text-sm bg-white border-t mt-auto",children:["© ",new Date().getFullYear()," RealHonest – get real, be real. All rights reserved."]})]})}function le({postId:t,maxComments:a=2}){const[r,u]=s.useState([]),[d,i]=s.useState(!0),[o,l]=s.useState(0);s.useEffect(()=>{h()},[t]);const h=async()=>{try{const c=await fetch(`/api/comments/${t}`,{credentials:"include"});if(c.ok){const m=await c.json();u(m.slice(0,a)),l(m.length)}}catch(c){console.error("Error fetching comments:",c)}finally{i(!1)}};return d?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500",children:"Loading comments..."})}):r.length===0?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500 italic",children:"No comments yet. Be the first to comment!"})}):e.jsxs("div",{className:"mt-4 space-y-3",children:[r.map((c,m)=>{var x;return e.jsx("div",{className:"bg-gray-50 p-3 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"w-6 h-6 bg-indigo-400 rounded-full flex items-center justify-center text-white text-xs font-semibold",children:((x=c.author)==null?void 0:x.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:c.author||"Anonymous"}),e.jsx("span",{className:"text-xs text-gray-500",children:new Date(c.created_at).toLocaleDateString()})]}),e.jsx("p",{className:"text-sm text-gray-700 mt-1 break-words",children:c.content})]})]})},c.id||m)}),o>a&&e.jsx("div",{className:"text-center",children:e.jsxs("a",{href:`/posts/${t}#comments`,className:"text-sm text-indigo-600 hover:text-indigo-800 font-medium",children:["View all ",o," comments →"]})})]})}const de=t=>`${window.location.origin}/posts/${t}`,ce=t=>{const a=t.content.replace(/<[^>]*>/g,"").slice(0,100);return`${t.title}

${a}${a.length>=100?"...":""}`},me=async t=>{try{return await navigator.clipboard.writeText(t),!0}catch(a){return console.error("Failed to copy to clipboard:",a),!1}},ue=(t,a=120)=>`https://api.qrserver.com/v1/create-qr-code/?size=${a}x${a}&data=${encodeURIComponent(t)}`,R={whatsapp:(t,a,r)=>`https://wa.me/?text=${encodeURIComponent(`${t}

${a}

${r}`)}`,email:(t,a,r)=>`mailto:?subject=${encodeURIComponent(t)}&body=${encodeURIComponent(`${a}

Read more: ${r}`)}`,twitter:(t,a,r)=>`https://twitter.com/intent/tweet?text=${encodeURIComponent(`${t}

${a}`)}&url=${encodeURIComponent(r)}`,facebook:(t,a,r)=>`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(r)}`,linkedin:(t,a,r)=>`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(r)}`,telegram:(t,a,r)=>`https://t.me/share/url?url=${encodeURIComponent(r)}&text=${encodeURIComponent(`${t}

${a}`)}`,reddit:(t,a,r)=>`https://reddit.com/submit?url=${encodeURIComponent(r)}&title=${encodeURIComponent(t)}`,pinterest:(t,a,r)=>`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(r)}&description=${encodeURIComponent(`${t}

${a}`)}`};function H({isOpen:t,onClose:a,post:r}){const[u,d]=s.useState(!1);if(!t||!r)return null;const i=de(r.id),o=r.title,l=ce(r),h=async()=>{await me(i)?(d(!0),setTimeout(()=>d(!1),2e3)):alert("Failed to copy link")},c=[{name:"WhatsApp",icon:"💬",color:"bg-green-500 hover:bg-green-600",url:R.whatsapp(o,l,i)},{name:"Email",icon:"📧",color:"bg-blue-500 hover:bg-blue-600",url:R.email(o,l,i)},{name:"Twitter",icon:"🐦",color:"bg-sky-500 hover:bg-sky-600",url:R.twitter(o,l,i)},{name:"Facebook",icon:"📘",color:"bg-blue-600 hover:bg-blue-700",url:R.facebook(o,l,i)},{name:"LinkedIn",icon:"💼",color:"bg-blue-700 hover:bg-blue-800",url:R.linkedin(o,l,i)},{name:"Telegram",icon:"✈️",color:"bg-blue-400 hover:bg-blue-500",url:R.telegram(o,l,i)},{name:"Reddit",icon:"🤖",color:"bg-orange-500 hover:bg-orange-600",url:R.reddit(o,l,i)},{name:"Pinterest",icon:"📌",color:"bg-red-500 hover:bg-red-600",url:R.pinterest(o,l,i)}],m=x=>{window.open(x,"_blank","width=600,height=400")};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Share Post"}),e.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 text-xl",children:"✕"})]}),e.jsxs("div",{className:"p-4 border-b bg-gray-50",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:o}),e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:l})]}),e.jsxs("div",{className:"p-4 border-b",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Copy Link"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:i,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"}),e.jsx("button",{onClick:h,className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${u?"bg-green-100 text-green-700":"bg-indigo-600 text-white hover:bg-indigo-700"}`,children:u?"✓ Copied!":"Copy"})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Share via"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:c.map(x=>e.jsxs("button",{onClick:()=>m(x.url),className:`flex items-center space-x-3 p-3 rounded-lg text-white transition-colors ${x.color}`,children:[e.jsx("span",{className:"text-lg",children:x.icon}),e.jsx("span",{className:"font-medium",children:x.name})]},x.name))})]}),e.jsxs("div",{className:"p-4 border-t bg-gray-50",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"QR Code"}),e.jsx("div",{className:"flex justify-center",children:e.jsx("img",{src:ue(i,120),alt:"QR Code",className:"w-24 h-24 border rounded"})}),e.jsx("p",{className:"text-xs text-gray-500 text-center mt-2",children:"Scan to open post"})]})]})})}function he({post:t,user:a}){var S;const[r,u]=s.useState(!1),[d,i]=s.useState({comments_count:0,user_saved:!1}),[o,l]=s.useState(!1),[h,c]=s.useState(!1),[m,x]=s.useState(""),[n,b]=s.useState(!1),[p,y]=s.useState(!1),j=I();s.useEffect(()=>{f()},[t.id]);const f=async()=>{try{const N=await fetch(`/api/posts/${t.id}/stats`,{credentials:"include"});if(N.ok){const _=await N.json();i(_)}}catch(N){console.error("Error fetching post stats:",N)}},g=async()=>{if(!a){alert("Please log in to save posts");return}l(!0);try{const N=await fetch(`/api/posts/${t.id}/save`,{method:"POST",credentials:"include"});if(N.ok){const _=await N.json();i(D=>({...D,user_saved:_.saved})),alert(_.saved?"Post saved!":"Post unsaved!")}}catch(N){console.error("Error toggling save:",N),alert("Failed to toggle save")}finally{l(!1)}},w=()=>{y(!0)},C=()=>{if(!a){alert("Please log in to comment");return}c(!h)},L=async N=>{if(N.preventDefault(),!!m.trim()){b(!0);try{const _=await fetch(`/api/comments/${t.id}`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({content:m})});if(_.ok)x(""),c(!1),f(),window.location.reload();else{const D=await _.json().catch(()=>({detail:"Failed to post comment"}));alert(`Failed to post comment: ${D.detail||"Unknown error"}`)}}catch(_){console.error("Error posting comment:",_),alert("Failed to post comment")}finally{b(!1)}}},F=t.content.replace(/<[^>]*>/g,""),k=F.length>200,v=r?t.content:F.length>200?t.content.slice(0,200)+"...":t.content;return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold",children:((S=t.author_username)==null?void 0:S.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:t.author_username||"Unknown"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(t.created_at).toLocaleDateString()})]})]}),e.jsx("h4",{className:"text-xl font-bold mb-3 text-gray-900",children:t.title}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full max-w-md mx-auto rounded-lg mb-4 cursor-pointer",onClick:()=>j(`/posts/${t.id}`)}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-gray-700 leading-relaxed prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:v}}),k&&e.jsx("button",{onClick:()=>u(!r),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2",children:r?"Show Less":"Read More"})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("button",{onClick:C,className:"flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"💬"}),e.jsx("span",{className:"text-sm font-medium",children:d.comments_count})]}),e.jsxs("button",{onClick:w,className:"flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:g,disabled:o,className:`transition-colors ${d.user_saved?"text-yellow-500 hover:text-yellow-600":"text-gray-500 hover:text-yellow-500"} ${o?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-lg",children:d.user_saved?"🔖":"📑"})}),e.jsx("button",{onClick:()=>j(`/posts/${t.id}`),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium",children:"View Full Post →"})]})]}),h&&a&&e.jsx("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:e.jsxs("form",{onSubmit:L,children:[e.jsx("textarea",{value:m,onChange:N=>x(N.target.value),placeholder:"Write a comment...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent",rows:"3",disabled:n}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-2",children:[e.jsx("button",{type:"button",onClick:()=>c(!1),className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800",disabled:n,children:"Cancel"}),e.jsx("button",{type:"submit",disabled:n||!m.trim(),className:"px-4 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed",children:n?"Posting...":"Post Comment"})]})]})}),e.jsx(le,{postId:t.id,maxComments:2}),e.jsx(H,{isOpen:p,onClose:()=>y(!1),post:t})]})}function xe({user:t}){const[a,r]=s.useState([]),[u,d]=s.useState("Loading fact..."),i=()=>{fetch("/api/posts").then(o=>o.json()).then(r).catch(o=>console.error("Error fetching posts:",o))};return s.useEffect(()=>{i(),fetch("https://numbersapi.com/random/trivia").then(o=>o.text()).then(d).catch(o=>{console.error("Failed to load number fact",o),d("There's always something new to learn.")})},[]),s.useEffect(()=>{const o=h=>{h.key==="postsUpdated"&&(i(),localStorage.removeItem("postsUpdated"))};window.addEventListener("storage",o);const l=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(i(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",l),()=>{window.removeEventListener("storage",o),document.removeEventListener("visibilitychange",l)}},[]),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"py-16 px-4 text-center bg-gradient-to-b from-indigo-50 to-white",children:[e.jsx("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600",children:"Real Honest Conversations for Visionary Minds"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto mb-8 text-gray-800",children:"A platform for scholars, entrepreneurs, and community leaders to share authentic insights, mentor others, and build a better world without the noise of likes or dislikes."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:t?e.jsx("a",{href:"/dashboard",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition",children:"Go to Dashboard"}):e.jsxs(e.Fragment,{children:[e.jsx("a",{href:"/login",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105",children:"Join Be Real, Get Real"}),e.jsx("a",{href:"#features",className:"px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition",children:"Learn More"})]})})]}),e.jsx("section",{id:"features",className:"py-16 px-4 bg-gray-100",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-2xl font-bold text-center mb-10",children:"Core Features"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"WebRTC Chat"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Secure peer-to-peer video/audio communication between mentors and mentees."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Offline Capabilities"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Built as a Progressive Web App (PWA) — use it anywhere, even without internet access."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"No Popularity Metrics"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Focus on authentic content and meaningful discussions — no likes, no fake stories, no popularity contests."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Community Building"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Create and join topic-based communities focused on research, education, or social impact."})]})]})]})}),e.jsxs("section",{className:"py-16 px-4 text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-8 text-gray-900 dark:text-gray-100",children:"Recent Community Insights"}),a.length>0?e.jsx("div",{className:"max-w-4xl mx-auto space-y-6",children:a.map(o=>e.jsx(he,{post:o,user:t},o.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function ge({onPostCreated:t}){const[a,r]=s.useState(""),[u,d]=s.useState(""),[i,o]=s.useState(null),[l,h]=s.useState(null),[c,m]=s.useState(!1),[x,n]=s.useState(""),[b,p]=s.useState(""),y=s.useRef(null),j=()=>{const k=document.createElement("input");k.setAttribute("type","file"),k.setAttribute("accept","image/*"),k.click(),k.onchange=async()=>{const v=k.files[0];if(v){const S=new FormData;S.append("image",v);try{const N=await fetch("/api/upload-image",{method:"POST",body:S});if(N.ok){const D=(await N.json()).url,$=y.current.getEditor(),T=$.getSelection();$.insertEmbed(T.index,"image",D)}else{const _=new FileReader;_.onload=D=>{const $=y.current.getEditor(),T=$.getSelection();$.insertEmbed(T.index,"image",D.target.result)},_.readAsDataURL(v)}}catch(N){console.error("Image upload failed:",N);const _=new FileReader;_.onload=D=>{const $=y.current.getEditor(),T=$.getSelection();$.insertEmbed(T.index,"image",D.target.result)},_.readAsDataURL(v)}}}},f=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:j}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),g=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"],w=k=>{const v=k.replace(/<[^>]*>/g,"").trim();return v?v.split(/\s+/).length:0},C=k=>{const v=w(k);return Math.ceil(v/200)},L=k=>{const v=k.target.files[0];if(v){o(v);const S=new FileReader;S.onloadend=()=>h(S.result),S.readAsDataURL(v)}},F=async k=>{if(k.preventDefault(),m(!0),n(""),p(""),!u.replace(/<[^>]*>/g,"").trim()){n("Please enter some content for your post"),m(!1);return}const S=new FormData;S.append("title",a),S.append("content",u),i&&S.append("image",i);try{const N=await fetch("/api/posts",{method:"POST",body:S,credentials:"include"});if(!N.ok){const _=await N.json().catch(()=>({detail:"Failed to create post"}));throw new Error(_.detail||"Something went wrong")}await N.json(),p("✅ Post published successfully!"),r(""),d(""),o(null),h(null),localStorage.setItem("postsUpdated",Date.now().toString()),setTimeout(t?()=>{t()}:()=>{window.location.href="/dashboard"},1e3)}catch(N){n(N.message||"❌ Error creating post")}finally{m(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded shadow-md mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"✍️ Create New Post"}),e.jsx("style",{jsx:!0,children:`
        .ql-editor {
          min-height: 250px;
          font-size: 14px;
          line-height: 1.6;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          background: #f9fafb;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: italic;
        }
        .ql-toolbar .ql-formats {
          margin-right: 8px;
        }
        .ql-toolbar button {
          padding: 4px;
          margin: 1px;
        }
        .ql-toolbar button:hover {
          background: #e5e7eb;
          border-radius: 3px;
        }
        .ql-toolbar .ql-active {
          background: #dbeafe;
          color: #1d4ed8;
          border-radius: 3px;
        }
        .ql-editor img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ql-editor blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 16px;
          margin: 16px 0;
          font-style: italic;
          color: #6b7280;
        }
        .ql-editor pre.ql-syntax {
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          padding: 12px;
          overflow-x: auto;
        }
        .ql-editor .ql-video {
          width: 100%;
          height: 315px;
        }
        .ql-snow .ql-tooltip {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .ql-snow .ql-tooltip input[type=text] {
          border: 1px solid #d1d5db;
          border-radius: 4px;
          padding: 4px 8px;
        }
        .ql-snow .ql-picker-options {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}),x&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:x}),b&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:b}),e.jsxs("form",{onSubmit:F,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:a,onChange:k=>r(k.target.value),required:!0,disabled:c,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",u.replace(/<[^>]*>/g,"").length," characters • ",w(u)," words • ~",C(u)," min read)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(W,{ref:y,theme:"snow",value:u,onChange:d,modules:f,formats:g,placeholder:"Write your post content here... Use the enhanced toolbar for rich formatting, images, and more!",readOnly:c,style:{minHeight:"300px",backgroundColor:c?"#f3f4f6":"white"}})}),u&&e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["✨ ",e.jsx("strong",{children:"Enhanced Features:"})," Images, videos, formulas, tables, colors, fonts, and more!"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["📸 ",e.jsx("strong",{children:"Images:"})," Click the image icon to upload • 🎨 ",e.jsx("strong",{children:"Colors:"})," Highlight text and use color tools • 📐 ",e.jsx("strong",{children:"Math:"})," Use formula button for equations"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:L,disabled:c,className:"w-full px-2 py-1 border border-gray-300 rounded"})]}),l&&e.jsx("img",{src:l,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:c,className:`w-full py-2 px-4 rounded text-white font-medium transition ${c?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:c?"Publishing...":"Publish Post"})]})]})}function pe({post:t,className:a="",children:r}){const[u,d]=s.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>d(!0),className:a,children:r||e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})}),e.jsx(H,{isOpen:u,onClose:()=>d(!1),post:t})]})}function be({user:t}){const[a,r]=s.useState(null),[u,d]=s.useState([]),[i,o]=s.useState("Loading fact..."),[l,h]=s.useState(!1),c=I();s.useEffect(()=>{t!=null&&t.github_username&&fetch(`https://api.github.com/users/${t.github_username}`).then(n=>n.json()).then(n=>r({followers:n.followers,public_repos:n.public_repos,profile_url:n.html_url})).catch(n=>console.error("Failed to fetch GitHub data",n))},[t]);const m=()=>{fetch("/api/posts").then(n=>n.json()).then(n=>d(n)).catch(n=>console.error(n))};s.useEffect(()=>{m()},[]),s.useEffect(()=>{const n=p=>{p.key==="postsUpdated"&&m()};window.addEventListener("storage",n);const b=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(m(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",b),()=>{window.removeEventListener("storage",n),document.removeEventListener("visibilitychange",b)}},[]),s.useEffect(()=>{fetch("https://numbersapi.com/random/trivia").then(n=>n.text()).then(o).catch(n=>{console.error("Failed to load number fact",n),o("There's always something new to learn.")})},[]);const x=async n=>{if(window.confirm("Are you sure you want to delete this post?"))try{const p=await fetch(`/api/posts/${n}`,{method:"DELETE",credentials:"include"});if(!p.ok){const y=await p.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(y.detail||"Something went wrong")}m()}catch(p){alert(p.message)}};return!t||t.role!=="author"?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Only authors can access dashboard"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8",children:[e.jsxs("h1",{className:"text-2xl font-extrabold",children:["Welcome back, ",t.full_name||t.username]}),e.jsx("p",{className:"mt-2 opacity-90",children:"Share insights, connect, and build honest conversations."})]}),!t.mentor_profile&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-8 transition-colors duration-300",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Want to Become a Mentor?"}),e.jsx("p",{className:"mb-4 text-gray-700 dark:text-gray-300",children:"Help others grow by offering guidance in your field."}),e.jsx("a",{href:"/register-mentor",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105","aria-label":"Register as a mentor to help others grow",children:"🎓 Register as Mentor"})]}),(t==null?void 0:t.github_username)&&a&&e.jsxs("div",{className:"mt-6 bg-white dark:bg-gray-800 p-4 rounded shadow-sm transition-colors duration-300",children:[e.jsx("h2",{className:"font-semibold mb-2 text-gray-900 dark:text-gray-100",children:"GitHub Profile"}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Followers: ",a.followers]}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Repositories: ",a.public_repos]}),e.jsx("p",{children:e.jsx("a",{href:a.profile_url,target:"_blank",rel:"noopener noreferrer",className:"text-indigo-600 dark:text-indigo-400 underline",children:"View on GitHub"})})]}),e.jsx("button",{onClick:()=>h(!l),type:"button",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition mb-6","aria-label":"Create a new post",children:"✏️ New Post"}),l&&e.jsx(ge,{onPostCreated:()=>{m(),h(!1),localStorage.setItem("postsUpdated",Date.now().toString())}}),e.jsxs("section",{className:"bg-indigo-50 p-4 rounded mb-6",children:[e.jsx("h3",{className:"font-semibold",children:"Today’s Number Fact"}),e.jsx("p",{children:i})]}),e.jsxs("section",{className:"space-y-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Recent Community Posts"}),u.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:u.map(n=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:n.title}),e.jsx("div",{className:"text-gray-700 mb-4 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:n.content.length>150?n.content.slice(0,150)+"...":n.content}}),n.image_url&&e.jsx("img",{src:n.image_url,alt:n.title,className:"w-full h-48 object-cover rounded mb-4",onError:b=>{b.target.style.display="none"}}),e.jsx("small",{className:"text-sm text-gray-500",children:new Date(n.created_at).toLocaleString()}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx("a",{href:`/posts/${n.id}`,className:"text-indigo-600 hover:underline",children:"View"}),e.jsx(pe,{post:n,className:"text-green-600 hover:underline",children:"📤 Share"}),e.jsx("button",{id:`edit-post-${n.id}`,onClick:()=>c(`/posts/${n.id}/edit`),"aria-label":`Edit ${n.title}`,className:"text-blue-600 hover:underline",children:"🖋️ Edit"}),e.jsx("button",{id:`delete-post-${n.id}`,onClick:()=>x(n.id),"aria-label":`Delete ${n.title}`,className:"text-red-600 hover:underline",children:"🗑️ Delete"})]})]},n.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function fe({onLogin:t}){const[a,r]=s.useState(""),[u,d]=s.useState(""),[i,o]=s.useState(""),l=async h=>{h.preventDefault();const c=new FormData;c.append("username",a),c.append("password",u);try{const m=await fetch("/login",{method:"POST",body:c,credentials:"include"});if(!m.ok){const n=await m.json();throw new Error(n.detail||"Invalid credentials")}const x=await m.json();t(x),window.location.href="/dashboard"}catch(m){o(m.message||"Failed to log in")}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Login to RealHonest"}),i&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",i]}),e.jsxs("form",{onSubmit:l,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",value:a,onChange:h=>r(h.target.value),required:!0,autoComplete:"username",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",value:u,onChange:h=>d(h.target.value),required:!0,autoComplete:"current-password",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsx("button",{type:"submit",className:"w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded",children:"Login"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx("a",{href:"/signup",className:"text-indigo-600 hover:underline",children:"Sign up"})]})]})}function ye({onLogin:t}){const[a,r]=s.useState(""),[u,d]=s.useState(""),[i,o]=s.useState(""),[l,h]=s.useState(""),[c,m]=s.useState(""),[x,n]=s.useState(!1),b=async p=>{p.preventDefault(),n(!0),h(""),m("");const y=new FormData;y.append("username",a),y.append("email",u),y.append("password",i);try{const j=await fetch("/signup",{method:"POST",body:y,credentials:"include",headers:{"X-Requested-With":"XMLHttpRequest"}});if(j.redirected){window.location.href=j.url;return}if(!j.ok){const g=await j.json().catch(()=>({detail:"Signup failed"}));throw new Error(g.detail||"Something went wrong")}const f=await j.json();f.redirect?window.location.href=f.redirect:window.location.href="/profile"}catch(j){h(j.message||"Failed to register")}finally{n(!1)}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Create Your Account"}),l&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",l]}),c&&e.jsxs("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:["✅ ",c]}),e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",autoComplete:"username",value:a,onChange:p=>r(p.target.value),required:!0,disabled:x,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{id:"email",type:"email",autoComplete:"email",value:u,onChange:p=>d(p.target.value),required:!0,disabled:x,placeholder:"<EMAIL>",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",autoComplete:"new-password",value:i,onChange:p=>o(p.target.value),required:!0,disabled:x,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsx("button",{type:"submit",disabled:x,className:`w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded transition ${x?"opacity-70 cursor-not-allowed":""}`,children:x?"Creating account...":"Sign Up"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Already have an account?"," ",e.jsx("a",{href:"/login",className:"text-indigo-600 hover:underline",children:"Login"})]})]})}function je({user:t}){const[a,r]=s.useState(""),[u,d]=s.useState("Research"),[i,o]=s.useState(""),[l,h]=s.useState(""),[c,m]=s.useState(""),[x,n]=s.useState(0),[b,p]=s.useState(""),[y,j]=s.useState(!0),f=async g=>{g.preventDefault();const w={full_name:a,category:u,expertise:i,bio:l,qualifications:c,experience_years:parseInt(x),hourly_rate:b,available:y};try{if(!(await fetch("/register-mentor",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(w),credentials:"include"})).ok)throw new Error("Registration failed");alert("Successfully registered as mentor!"),window.location.href="/profile"}catch(C){console.error("Error registering:",C),alert("Failed to register mentor.")}};return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Become a Mentor"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Help others grow by offering guidance in your field."}),e.jsxs("form",{onSubmit:f,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:a,onChange:g=>r(g.target.value),required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mentor Category"}),e.jsx("select",{id:"category",value:u,onChange:g=>d(g.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none",required:!0,children:["Research","Entrepreneurship","Education","Social Impact"].map(g=>e.jsx("option",{value:g,children:g},g))})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expertise"}),e.jsx("input",{id:"expertise",type:"text",value:i,onChange:g=>o(g.target.value),required:!0,placeholder:"e.g., AI Ethics, Startup Strategy",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:l,onChange:g=>h(g.target.value),required:!0,placeholder:"What do you specialize in?",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"qualifications",className:"block text-sm font-medium text-gray-700 mb-2",children:"Qualifications"}),e.jsx("textarea",{id:"qualifications",rows:"4",value:c,onChange:g=>m(g.target.value),required:!0,placeholder:"e.g., PhD in Computer Science, 10+ years experience",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"experience_years",className:"block text-sm font-medium text-gray-700 mb-2",children:"Years of Experience"}),e.jsx("input",{id:"experience_years",type:"number",min:"0",max:"50",value:x,onChange:g=>n(parseInt(g.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"hourly_rate",className:"block text-sm font-medium text-gray-700 mb-2",children:"Hourly Rate"}),e.jsx("input",{id:"hourly_rate",type:"text",value:b,onChange:g=>p(g.target.value),placeholder:"e.g., $50/hour",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:y,onChange:()=>j(!y),className:"mr-2"}),e.jsx("label",{htmlFor:"available",className:"text-sm text-gray-700",children:"I am available for mentoring sessions"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",className:"px-5 py-2 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-md shadow transition",children:"Register as Mentor"})})]})]})})}function we({user:t}){const{mentorId:a}=A(),[r,u]=s.useState(null),[d,i]=s.useState(!0);return s.useEffect(()=>{fetch(`/api/mentors/${a}`).then(o=>o.json()).then(u).finally(()=>i(!1))},[a]),d?e.jsx("p",{children:"Loading mentor profile..."}):r?e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:r.full_name}),e.jsxs("p",{className:"text-indigo-600 font-medium mt-1",children:["Category: ",r.category]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Expertise:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.expertise})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Bio:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.bio||"No bio provided."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Qualifications:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.qualifications||"Not specified."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Experience:"}),e.jsxs("p",{className:"mt-2 text-gray-700",children:[r.experience_years," years"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Hourly Rate:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.hourly_rate})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:`/dm/${r.user.username}`,className:"px-4 py-2 bg-indigo-600 text-white rounded-md transition",children:"💬 Message Mentor"}),(t==null?void 0:t.role)==="author"&&e.jsx("a",{href:`/edit-mentor/${r.id}`,className:"px-4 py-2 bg-green-600 text-white rounded-md transition",children:"✏️ Edit Profile"})]})]})}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Mentor not found"}),e.jsx("a",{href:"/mentors",className:"text-indigo-600 underline",children:"Browse All Mentors"})]})}function ve({user:t}){const[a,r]=s.useState([]),[u,d]=s.useState(""),i=s.useRef(null),o=s.useRef(null);s.useEffect(()=>{var m;if(!t)return;const c=(m=document.cookie.split("; ").find(x=>x.startsWith("access_token=")))==null?void 0:m.split("=")[1];o.current=new WebSocket("ws://localhost:8000/ws/chat"),o.current.onopen=()=>{o.current.send(JSON.stringify({token:c}))},o.current.onmessage=x=>{try{const n=JSON.parse(x.data);r(b=>[...b,n])}catch{console.error("Failed to parse message:",x.data)}}},[t]);const l=c=>{var x;if(c.preventDefault(),!u.trim())return;if(h(u)){alert("Your message contains restricted content.");return}const m={sender:(t==null?void 0:t.username)||"Anonymous",text:u,timestamp:new Date().toISOString()};((x=o.current)==null?void 0:x.readyState)===WebSocket.OPEN?o.current.send(JSON.stringify(m)):console.warn("WebSocket not connected"),r(n=>[...n,m]),d("")},h=c=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(x=>c.toLowerCase().includes(x));return s.useEffect(()=>{i.current&&(i.current.scrollTop=i.current.scrollHeight)},[a]),t?e.jsxs("main",{className:"max-w-4xl mx-auto p-6",children:[e.jsxs("section",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Video Chat with Mentor"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"localVideo",autoPlay:!0,muted:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"You"})]}),e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"remoteVideo",autoPlay:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"Remote"})]})]}),e.jsxs("div",{className:"mt-4 flex justify-center space-x-4",children:[e.jsx("button",{id:"startCallBtn",type:"button","aria-label":"Start video call with mentor",className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-md transition",children:"Start Call"}),e.jsx("button",{id:"endCallBtn",type:"button","aria-label":"End video call",className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md shadow-md transition",children:"End Call"})]})]}),e.jsxs("section",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Chat Messages"}),e.jsx("div",{id:"messageList",ref:i,className:"space-y-4 max-h-96 overflow-y-auto p-2 border border-gray-200 rounded",children:a.length>0?a.map((c,m)=>e.jsx("div",{className:`py-2 ${c.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded-lg ${c.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:c.sender}),": ",c.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(c.timestamp).toLocaleTimeString()})]})},m)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{id:"chatForm",onSubmit:l,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{id:"messageInput",type:"text",value:u,onChange:c=>d(c.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition",children:"Send"})]})]})]}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Please log in to use the chat"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]})}function Ne({user:t}){const[a,r]=s.useState([]),[u,d]=s.useState(""),i=s.useRef(null);s.useEffect(()=>{const h=new WebSocket(`ws://localhost:8000/ws/dm/${t.username}?token=Bearer%20${localStorage.getItem("access_token")}`);h.onmessage=c=>{try{const m=JSON.parse(c.data);r(x=>[...x,m])}catch{console.error("Failed to parse:",c.data)}}},[t]);const o=h=>{if(h.preventDefault(),!u.trim())return;if(l(u)){alert("Your message contains restricted words");return}const c={sender:t.username,receiver:t.username,text:u,timestamp:new Date().toISOString()};ws.readyState===WebSocket.OPEN&&ws.send(JSON.stringify(c)),r(m=>[...m,c]),d("")},l=h=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(m=>h.toLowerCase().includes(m));return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("section",{className:"bg-white p-6 rounded shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Message Allen"}),e.jsx("div",{ref:i,className:"space-y-3 max-h-96 overflow-y-auto p-4 border border-gray-200 rounded mb-4",children:a.length>0?a.map((h,c)=>e.jsx("div",{className:`py-2 ${h.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded ${h.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:h.sender}),": ",h.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(h.timestamp).toLocaleTimeString()})]})},c)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{onSubmit:o,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{type:"text",value:u,onChange:h=>d(h.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700",children:"Send"})]})]})})}function ke(){const[t,a]=s.useState([]),[r,u]=s.useState(""),[d,i]=s.useState(!0),[o,l]=s.useState(null);s.useEffect(()=>{async function c(){try{const m=await fetch("/api/mentors");if(!m.ok)throw new Error("Failed to load mentors");const x=await m.json();a(x)}catch(m){l(m.message)}finally{i(!1)}}c()},[]);const h=async()=>{if(r.trim())try{const c=await fetch(`/api/mentors/country?country=${r}`);if(!c.ok)throw new Error("No mentors found");const m=await c.json();a(m.mentors||[])}catch{alert("No mentors found for that country.")}};return d?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading mentors..."})}):o?e.jsxs("div",{className:"text-red-500 text-center py-10",children:[e.jsx("p",{children:o}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 underline",children:"Try again"})]}):e.jsxs("main",{className:"max-w-6xl mx-auto p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Find a Mentor"}),e.jsxs("form",{onSubmit:c=>{c.preventDefault(),h()},className:"mb-8 flex gap-2 max-w-md mx-auto",children:[e.jsx("input",{type:"text",value:r,onChange:c=>u(c.target.value),placeholder:"Search mentors by country...",className:"w-full px-4 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"🔍"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.length>0?t.map((c,m)=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition",children:[e.jsx("h3",{className:"text-xl font-semibold",children:c.full_name}),e.jsx("p",{className:"text-indigo-600 font-medium mt-1",children:c.category}),e.jsx("p",{className:"mt-2 text-gray-700 line-clamp-2",children:c.expertise}),e.jsxs("p",{className:"mt-2 text-sm text-gray-500",children:["Experience: ",c.experience_years," years"]}),e.jsx(q,{to:`/mentors/${c.id}`,className:"mt-4 inline-block text-indigo-600 hover:text-indigo-800 hover:underline",children:"View Profile"})]},m)):e.jsx("p",{className:"italic text-gray-500 col-span-full text-center py-10",children:"No mentors found. Try searching by country or check back later."})})]})}function V({postId:t}){const[a,r]=s.useState([]),[u,d]=s.useState(""),[i,o]=s.useState(1),[l,h]=s.useState(!0),[c,m]=s.useState(!1),x=async p=>{const j=await(await fetch(`/api/comments/${t}?page=${p}`)).json();j.length===0&&h(!1),r(f=>[...f,...j])};s.useEffect(()=>{x(i)},[i]);const n=()=>{l&&!c&&o(p=>p+1)},b=async p=>{p.preventDefault();try{const y=await fetch(`/api/comments/${t}`,{method:"POST",body:JSON.stringify({content:u}),headers:{"Content-Type":"application/json"},credentials:"include"});if(y.ok){const j=await y.json();r([j,...a]),d("")}else{const j=await y.json().catch(()=>({detail:"Failed to submit comment"}));alert(`Failed to submit comment: ${j.detail||"Unknown error"}`)}}catch(y){console.error("Error submitting comment:",y),alert("Failed to submit comment: Network error")}};return e.jsxs("section",{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Comments"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[a.map(p=>e.jsxs("div",{className:"bg-gray-50 p-4 rounded border",children:[e.jsx("p",{className:"text-gray-800",children:p.content}),e.jsxs("small",{className:"text-sm text-gray-500",children:["— ",p.author," • ",new Date(p.created_at).toLocaleString()]})]},p.id)),l&&e.jsx("button",{onClick:n,className:"text-indigo-600 hover:text-indigo-800 underline text-sm",children:"Load More Comments"})]}),e.jsxs("form",{onSubmit:b,className:"space-y-4",children:[e.jsx("textarea",{value:u,onChange:p=>d(p.target.value),placeholder:"Add a comment...",className:"w-full px-4 py-2 border border-gray-300 rounded",required:!0}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded",children:"Submit"})]})]})}function Se({user:t}){const{id:a}=A(),[r,u]=s.useState(null),[d,i]=s.useState(!1),[o,l]=s.useState(!1),h=I();s.useEffect(()=>{fetch(`/api/posts/${a}`).then(n=>n.json()).then(u).catch(n=>console.error("Error fetching post:",n))},[a]);const c=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${a}`,{method:"DELETE",credentials:"include"});if(!b.ok){const p=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(p.detail||"Something went wrong")}alert("Post deleted successfully!"),h("/dashboard")}catch(b){alert(b.message)}},m=()=>{h(`/posts/${a}/edit`)},x=async()=>{if(!t){alert("Please log in to save posts");return}l(!0);try{const n=await fetch(`/api/posts/${r.id}/save`,{method:"POST",credentials:"include"});if(n.ok){const b=await n.json();u(p=>({...p,user_saved:b.saved})),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(n){console.error("Error toggling save:",n),alert("Failed to toggle save")}finally{l(!1)}};return r?e.jsx("main",{role:"main",id:"main-content",className:"max-w-3l mx auto",children:e.jsxs("div",{className:"max-w-3xl mx-auto p-6 bg-white rounded shadow",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:r.title}),r.image_url&&e.jsx("img",{src:r.image_url,alt:r.title,className:"w-full h-auto rounded mb-4"}),e.jsx("div",{className:"mb-6 prose prose-lg max-w-none",dangerouslySetInnerHTML:{__html:r.content}}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-6",children:["By ",r.author_username," • ",new Date(r.created_at).toLocaleString()]}),e.jsxs("div",{className:"flex gap-4 mb-6",children:[t&&e.jsx("button",{onClick:x,disabled:o,className:`px-4 py-2 rounded transition ${r.user_saved?"bg-indigo-600 hover:bg-indigo-700 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"} ${o?"opacity-50 cursor-not-allowed":""}`,children:o?"⏳":r.user_saved?"🔖 Saved":"📌 Save Post"}),e.jsx("button",{onClick:()=>i(!0),className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"📤 Share Post"}),t&&(t.username===r.author_username||t.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:m,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition",children:"🖋️ Edit Post"}),e.jsx("button",{onClick:c,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition",children:"🗑️ Delete Post"})]})]}),e.jsx(V,{postId:r.id}),e.jsx(H,{isOpen:d,onClose:()=>i(!1),post:r})]})}):e.jsx("p",{children:"Loading..."})}function Ce({post:t,user:a,showActions:r=!1,onPostUpdate:u}){const d=I(),[i,o]=s.useState(!1),[l,h]=s.useState(t),c=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${t.id}`,{method:"DELETE",credentials:"include"});if(!b.ok){const p=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(p.detail||"Something went wrong")}alert("Post deleted successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),window.location.reload()}catch(b){alert(b.message)}},m=()=>{d(`/posts/${t.id}/edit`)},x=async()=>{if(!a){alert("Please log in to save posts");return}o(!0);try{const n=await fetch(`/api/posts/${l.id}/save`,{method:"POST",credentials:"include"});if(n.ok){const b=await n.json();h(p=>({...p,user_saved:b.saved})),u&&u(l.id,{user_saved:b.saved}),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(n){console.error("Error toggling save:",n),alert("Failed to toggle save")}finally{o(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:t.title}),e.jsx("div",{className:"text-gray-700 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:t.content.length>200?t.content.slice(0,200)+"...":t.content}}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full h-40 object-cover rounded mb-4"}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-3",children:["By ",l.author_username," • ",new Date(l.created_at).toLocaleDateString()]}),e.jsxs("div",{className:"flex gap-2 items-center flex-wrap",children:[e.jsx("a",{href:`/posts/${l.id}`,className:"text-indigo-600 hover:underline",children:"View Post"}),a&&e.jsx("button",{onClick:x,disabled:i,className:`text-sm px-2 py-1 rounded transition ${l.user_saved?"text-indigo-600 bg-indigo-50":"text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"} ${i?"opacity-50 cursor-not-allowed":""}`,children:i?"⏳":l.user_saved?"🔖 Saved":"📌 Save"}),r&&a&&(a.username===l.author_username||a.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:m,className:"text-blue-600 hover:underline text-sm",children:"🖋️ Edit"}),e.jsx("button",{onClick:c,className:"text-red-600 hover:underline text-sm",children:"🗑️ Delete"})]})]})]})}function Ee({initialPosts:t=[]}){const[a,r]=s.useState(t),[u,d]=s.useState(!0),[i,o]=s.useState(1),[l,h]=s.useState(!1),[c,m]=s.useState(""),[x,n]=s.useState(""),[b,p]=s.useState("latest"),y=async()=>{if(!(!u||l)){h(!0);try{const f=await(await fetch(`/api/posts?page=${i}&limit=6`)).json();f.length===0&&d(!1),r(g=>[...g,...f]),o(g=>g+1)}catch{console.error("Failed to load more posts")}finally{h(!1)}}};return useEffect(()=>{const j=()=>{window.innerHeight+window.scrollY>=document.body.offsetHeight-500&&u&&!l&&y()};return window.addEventListener("scroll",j),()=>window.removeEventListener("scroll",j)},[u,l]),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(j=>e.jsx(Ce,{post:j},j.id))}),l&&e.jsx("p",{className:"text-center",children:"Loading more posts..."}),!u&&e.jsx("p",{className:"text-center text-gray-500 italic",children:"No more posts to load"})]})}function Pe({user:t}){const[a,r]=s.useState([]),[u,d]=s.useState(!0),[i,o]=s.useState(null);return s.useEffect(()=>{if(!(t!=null&&t.username)){d(!1);return}d(!0),o(null),fetch(`/api/activity/${t.username}`).then(l=>{if(!l.ok)throw new Error("Failed to fetch activities");return l.json()}).then(l=>{r(l||[]),d(!1)}).catch(l=>{console.error("Error fetching activities:",l),o(l.message),r([]),d(!1)})},[t]),t?u?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsx("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(l=>e.jsx("div",{className:"bg-gray-200 h-16 rounded"},l))})]})}):i?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading activities: ",i]}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),a.length>0?e.jsx("div",{className:"space-y-4",children:a.map((l,h)=>e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-indigo-500 hover:shadow-md transition-shadow",children:[e.jsx("p",{className:"text-gray-900 dark:text-gray-100 font-medium",children:l.action}),e.jsx("small",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(l.timestamp).toLocaleString()})]},h))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"No activity yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Start creating posts and interacting to see your activity here!"}),e.jsx("a",{href:"/dashboard",className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Go to Dashboard"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your activity feed."})})})}function _e({user:t}){const[a,r]=s.useState([]),[u,d]=s.useState(!0),[i,o]=s.useState(null),l=I();s.useEffect(()=>{if(!t){d(!1);return}h()},[t]);const h=async()=>{try{d(!0),o(null);const m=await fetch("/api/saved-posts",{credentials:"include"});if(!m.ok)throw new Error("Failed to load saved posts");const x=await m.json();r(x)}catch(m){console.error("Error fetching saved posts:",m),o(m.message)}finally{d(!1)}},c=async m=>{try{if((await fetch(`/api/posts/${m}/save`,{method:"POST",credentials:"include"})).ok)r(n=>n.filter(b=>b.id!==m));else throw new Error("Failed to unsave post")}catch(x){console.error("Error unsaving post:",x),alert("Failed to unsave post")}};return t?u?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),e.jsx("div",{className:"animate-pulse space-y-6",children:[1,2,3].map(m=>e.jsx("div",{className:"bg-gray-200 h-32 rounded"},m))})]})}):i?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading saved posts: ",i]}),e.jsx("button",{onClick:h,className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),a.length>0?e.jsx("div",{className:"space-y-6",children:a.map(m=>e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-gray-100",children:m.title}),e.jsx("button",{onClick:()=>c(m.id),className:"text-indigo-600 hover:text-red-600 transition-colors p-2 rounded hover:bg-red-50",title:"Remove from saved",children:"🔖"})]}),m.image_url&&e.jsx("img",{src:m.image_url,alt:m.title,className:"w-full h-48 object-cover rounded mb-4"}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 mb-4 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:m.content.length>200?m.content.slice(0,200)+"...":m.content}}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{children:["By ",m.author_username," • ",new Date(m.created_at).toLocaleDateString()]}),e.jsxs("span",{children:["Saved ",new Date(m.saved_at).toLocaleDateString()]})]}),e.jsx("div",{className:"mt-4 flex gap-2",children:e.jsx("button",{onClick:()=>l(`/posts/${m.id}`),className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"Read Full Post"})})]},m.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📌"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"No saved posts yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Start saving posts you want to read later!"}),e.jsx("button",{onClick:()=>l("/dashboard"),className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Browse Posts"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your saved posts."})})})}function Fe({postId:t}){const[a,r]=s.useState(""),[u,d]=s.useState(""),[i,o]=s.useState(null),[l,h]=s.useState(null),[c,m]=s.useState(!1),[x,n]=s.useState(""),{id:b}=A(),p=I();s.useEffect(()=>{fetch(`/api/posts/${t}`).then(f=>f.json()).then(f=>{r(f.title),d(f.content),h(f.image_url)})},[t]);const y=f=>{const g=f.target.files[0];if(g){o(g);const w=new FileReader;w.onloadend=()=>h(w.result),w.readAsDataURL(g)}},j=async f=>{f.preventDefault(),m(!0),n(""),p(`/posts/${b}`);const g=new FormData;g.append("title",a),g.append("content",u),i&&g.append("image",i);try{if(!(await fetch(`/api/posts/${t}`,{method:"POST",body:g,credentials:"include"})).ok)throw new Error("Update failed");alert("Post updated!"),window.location.href=`/posts/${t}`}catch(w){n(w.message||"Error updating post")}finally{m(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Edit Your Post"}),x&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:x}),e.jsxs("form",{onSubmit:j,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:a,onChange:f=>r(f.target.value),required:!0,disabled:c,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:"Content"}),e.jsx("textarea",{id:"content",rows:"5",value:u,onChange:f=>d(f.target.value),required:!0,disabled:c,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Upload New Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:y,disabled:c,className:"w-full px-2 py-1 border border-gray-300 rounded disabled:bg-gray-100"})]}),l&&e.jsx("img",{src:l,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:c,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${c?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:c?"Saving...":"Save Changes"})]})]})}function Le({user:t}){const{postId:a}=A(),[r,u]=s.useState(null),[d,i]=s.useState(""),[o,l]=s.useState(""),[h,c]=s.useState(null),[m,x]=s.useState(!0),n=I(),b=s.useRef(null),p=()=>{const g=document.createElement("input");g.setAttribute("type","file"),g.setAttribute("accept","image/*"),g.click(),g.onchange=async()=>{const w=g.files[0];if(w){const C=new FormData;C.append("image",w);try{const L=await fetch("/api/upload-image",{method:"POST",body:C});if(L.ok){const k=(await L.json()).url,v=b.current.getEditor(),S=v.getSelection();v.insertEmbed(S.index,"image",k)}else{const F=new FileReader;F.onload=k=>{const v=b.current.getEditor(),S=v.getSelection();v.insertEmbed(S.index,"image",k.target.result)},F.readAsDataURL(w)}}catch(L){console.error("Image upload failed:",L);const F=new FileReader;F.onload=k=>{const v=b.current.getEditor(),S=v.getSelection();v.insertEmbed(S.index,"image",k.target.result)},F.readAsDataURL(w)}}}},y=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:p}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),j=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"];s.useEffect(()=>{async function g(){const w=await fetch(`/api/posts/${a}`,{credentials:"include"});if(!w.ok){n("/dashboard");return}const C=await w.json();if(C.author_username!==(t==null?void 0:t.username)&&(t==null?void 0:t.role)!=="admin"){n("/dashboard");return}i(C.title),l(C.content),u(C),x(!1)}t&&g()},[a,t,n]);const f=async g=>{g.preventDefault();const w=new FormData;w.append("title",d),w.append("content",o),h&&w.append("post_image",h);try{const C=await fetch(`/api/posts/${a}`,{method:"POST",body:w,credentials:"include"});if(C.ok)alert("Post updated successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),n(`/posts/${a}`);else{const L=await C.json().catch(()=>({detail:"Failed to update post"}));alert(`Failed to update post: ${L.detail||"Unknown error"}`)}}catch(C){console.error("Error updating post:",C),alert(`Network error – could not update post: ${C.message}`)}};return m?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading post..."})}):e.jsxs("main",{className:"container mx-auto py-10 px-4 max-w-3xl",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Edit Your Post"}),e.jsxs("form",{onSubmit:f,encType:"multipart/form-data",className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",name:"title",value:d,onChange:g=>i(g.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",o.replace(/<[^>]*>/g,"").length," characters)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(W,{ref:b,theme:"snow",value:o,onChange:l,modules:y,formats:j,placeholder:"Edit your post content here...",style:{minHeight:"300px",backgroundColor:"white"}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"post_image",className:"block text-sm font-medium text-gray-700 mb-1",children:"Update Image (Optional)"}),e.jsx("input",{id:"post_image",type:"file",name:"post_image",accept:"image/*",onChange:g=>c(g.target.files[0]),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md transition",children:"Update Post"})]})]})}function De(){const[t,a]=s.useState(""),[r,u]=s.useState(""),[d,i]=s.useState(""),[o,l]=s.useState("/static/avatars/default.png"),[h,c]=s.useState(!1),m=n=>{const b=n.target.files[0];if(b){const p=new FileReader;p.onload=()=>l(p.result),p.readAsDataURL(b)}},x=async n=>{n.preventDefault(),c(!0);const b=new FormData;b.append("full_name",t),b.append("bio",r),b.append("github_username",d),n.target.avatar.files.length>0&&b.append("avatar",n.target.avatar.files[0]);try{console.log("Submitting profile data:",{fullName:t,bio:r,github:d});const p=await fetch("/create-profile",{method:"POST",credentials:"include",body:b});if(console.log("Response status:",p.status),p.ok)console.log("Profile created successfully"),alert("Profile saved successfully!"),window.location.href="/dashboard";else{const y=await p.text();console.error("Profile creation failed:",y),alert(`Update failed: ${y}`)}}catch(p){console.error("Profile creation error:",p),alert(`Error updating profile: ${p.message}`)}finally{c(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),e.jsxs("form",{onSubmit:x,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{type:"text",value:t,onChange:n=>a(n.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{rows:"4",value:r,onChange:n=>u(n.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"GitHub Username"}),e.jsx("input",{type:"text",value:d,onChange:n=>i(n.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:o,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:m,accept:"image/*"})]}),e.jsx("button",{type:"submit",className:"mt-4 w-full py-2 px-4 rounded bg-indigo-600 hover:bg-indigo-700 text-white",disabled:h,children:h?"Saving...":"Save Profile"})]})]})}function $e({user:t}){const[a,r]=s.useState((t==null?void 0:t.full_name)||""),[u,d]=s.useState((t==null?void 0:t.bio)||""),[i,o]=s.useState((t==null?void 0:t.github_username)||""),[l,h]=s.useState((t==null?void 0:t.avatar_url)||"/static/avatars/default.png"),[c,m]=s.useState(!1),[x,n]=s.useState(!1),[b,p]=s.useState(""),y=f=>{const g=f.target.files[0];if(g){const w=new FileReader;w.onload=()=>h(w.result),w.readAsDataURL(g)}},j=async f=>{f.preventDefault(),n(!0),p(""),m(!1);const g=new FormData;g.append("full_name",a),g.append("bio",u),g.append("github_username",i),image&&g.append("image",image),f.target.avatar.files.length>0&&g.append("avatar",f.target.avatar.files[0]);try{if(!(await fetch("/create-profile",{method:"POST",credentials:"include",body:g})).ok)throw new Error("Failed to update profile");m(!0),setTimeout(()=>{window.location.href="/dashboard"},1e3)}catch(w){p(w.message||"Error updating profile")}finally{n(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),c&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded",children:"✅ Profile updated successfully!"}),b&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:["❌ ",b]}),e.jsxs("form",{onSubmit:j,className:"space-y-4",children:[e.jsx("form",{action:"/create-profile",method:"post",enctype:"multipart/form-data"}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:a,onChange:f=>r(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:u,onChange:f=>d(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"github_username",className:"block text-sm font-medium mb-1",children:"GitHub Username (optional)"}),e.jsx("input",{id:"github_username",type:"text",value:i,onChange:f=>o(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:l,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:y,accept:"image/*",disabled:x,ImageUpload:!0,onUpload:f=>setImage(f),className:"w-full px-2 py-1 border rounded"})]}),e.jsx("button",{type:"submit",disabled:x,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${x?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:x?"Saving Changes...":"Save Profile"})]})]})}function Re({user:t}){return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"No archived chats yet."}),e.jsx("p",{children:"You can view past chat logs here once they are implemented."})]})})}function Ie({user:t}){const[a,r]=s.useState(!1),[u,d]=s.useState(null);s.useEffect(()=>{const o=l=>{l.preventDefault(),d(l),r(!0)};return window.addEventListener("beforeinstallprompt",o),r(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone===!0||window.matchMedia("(prefers-app-theme: dark)").matches),()=>window.removeEventListener("beforeinstallprompt",o)},[]);const i=o=>{o.preventDefault(),u&&u.prompt&&u.prompt()};return e.jsxs("main",{className:"max-w-xl mx-auto p-6 bg-white rounded shadow-md mt-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Download RealHonest"}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Install as Web App"}),e.jsx("p",{className:"mb-4 text-gray-700",children:"RealHonest works great on all devices. You can install it like a native app."}),a?e.jsx("button",{type:"button",onClick:i,className:"w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"💾 Install App"}):e.jsx("div",{className:"bg-blue-50 text-blue-700 p-3 rounded",children:e.jsx("p",{className:"text-sm",children:'To install, tap the browser menu and select "Add to Home Screen" or use the desktop app install option.'})})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📲 Native Mobile App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"RealHonest will soon be available on iOS and Android."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white text-center rounded transition",children:"📱 Download for iOS (Coming Soon)"}),e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white text-center rounded transition",children:"📱 Download for Android (Coming Soon)"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"💻 Desktop App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"Use RealHonest on desktop platforms today."}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Windows"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"macOS"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Linux"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Scan to Access"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Use this QR code to easily share the app with others."}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:"https://quickchart.io/qr?text=http://localhost:5173&size=150",alt:"QR Code for RealHonest",className:"w-40 h-40 border p-2 rounded bg-white shadow-md"})})]}),e.jsx("div",{className:"text-center",children:e.jsx("a",{href:"/profile",className:"inline-block text-indigo-600 hover:underline",children:"← Back to Profile"})})]})}function Me(){const[t,a]=s.useState(null),[r,u]=s.useState(!0);return s.useEffect(()=>{fetch("/api/user",{credentials:"include"}).then(d=>{if(d.ok)return d.json();throw new Error("Not authenticated")}).then(d=>a(d)).catch(()=>a(null)).finally(()=>u(!1))},[]),r?e.jsx("div",{className:"flex justify-center items-center h-screen bg-gray-50",children:e.jsx("p",{children:"Loading..."})}):e.jsx(ne,{children:e.jsxs(J,{children:[e.jsx(E,{path:"/",element:e.jsx(P,{user:t,children:e.jsx(xe,{user:t})})}),e.jsx(E,{path:"/dashboard",element:e.jsx(P,{user:t,children:e.jsx(be,{user:t})})}),e.jsx(E,{path:"/login",element:e.jsx(P,{user:t,children:e.jsx(fe,{onLogin:a})})}),e.jsx(E,{path:"/signup",element:e.jsx(P,{user:t,children:e.jsx(ye,{onLogin:a})})}),e.jsx(E,{path:"/posts/:id",element:e.jsx(P,{user:t,children:e.jsx(Se,{user:t})})}),e.jsx(E,{path:"/posts/:postId/edit",element:e.jsx(P,{user:t,children:e.jsx(Le,{user:t})})}),e.jsx(E,{path:"/register-mentor",element:e.jsx(P,{user:t,children:e.jsx(je,{user:t})})}),e.jsx(E,{path:"/mentors/:mentorId",element:e.jsx(P,{user:t,children:e.jsx(we,{user:t})})}),e.jsx(E,{path:"/mentors",element:e.jsx(P,{user:t,children:e.jsx(ke,{user:t})})}),e.jsx(E,{path:"/chat",element:e.jsx(P,{user:t,children:e.jsx(ve,{user:t})})}),e.jsx(E,{path:"/dm/:username",element:e.jsx(P,{user:t,children:e.jsx(Ne,{user:t})})}),e.jsx(E,{path:"/profile",element:e.jsx(P,{user:t,children:e.jsx(De,{user:t})})}),e.jsx(E,{path:"/activity",element:e.jsx(P,{user:t,children:e.jsx(Pe,{user:t})})}),e.jsx(E,{path:"/saved-posts",element:e.jsx(P,{user:t,children:e.jsx(_e,{user:t})})}),e.jsx(E,{path:"/posts",elements:e.jsx(P,{user:t,children:e.jsx(Ee,{user:t})})}),e.jsx(E,{path:"/editpostform",element:e.jsx(P,{user:t,children:e.jsx(Fe,{})})}),e.jsx(E,{path:"/profile",element:e.jsx(P,{children:e.jsx($e,{user:t})})}),e.jsx(E,{path:"/archived-chats",element:e.jsx(P,{children:e.jsx(Re,{user:t})})}),e.jsx(E,{path:"/download",element:e.jsx(P,{children:e.jsx(Ie,{user:t})})}),e.jsx(E,{path:"/comments",element:e.jsx(P,{children:e.jsx(V,{user:t})})})]})})}const Te=U.createRoot(document.getElementById("root"));Te.render(e.jsx(Q,{children:e.jsx(Me,{})}));"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(t=>console.log("Service Worker registered:",t.scope)).catch(t=>console.error("Service Worker registration failed:",t))});
