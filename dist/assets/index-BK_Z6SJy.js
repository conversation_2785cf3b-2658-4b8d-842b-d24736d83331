import{r as s,a as K,b as T}from"./vendor-DXBhkOeJ.js";import{L as I,u as A,a as q,B as Y,R as Z,b as C}from"./router-xL491rWF.js";import{R as z}from"./quill-C38wxQcs.js";(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))d(l);new MutationObserver(l=>{for(const o of l)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&d(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const o={};return l.integrity&&(o.integrity=l.integrity),l.referrerPolicy&&(o.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?o.credentials="include":l.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function d(l){if(l.ep)return;l.ep=!0;const o=r(l);fetch(l.href,o)}})();var G={exports:{}},O={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ee=s,te=Symbol.for("react.element"),se=Symbol.for("react.fragment"),ae=Object.prototype.hasOwnProperty,re=ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ne={key:!0,ref:!0,__self:!0,__source:!0};function J(t,a,r){var d,l={},o=null,i=null;r!==void 0&&(o=""+r),a.key!==void 0&&(o=""+a.key),a.ref!==void 0&&(i=a.ref);for(d in a)ae.call(a,d)&&!ne.hasOwnProperty(d)&&(l[d]=a[d]);if(t&&t.defaultProps)for(d in a=t.defaultProps,a)l[d]===void 0&&(l[d]=a[d]);return{$$typeof:te,type:t,key:o,ref:i,props:l,_owner:re.current}}O.Fragment=se;O.jsx=J;O.jsxs=J;G.exports=O;var e=G.exports,H={},W=K;H.createRoot=W.createRoot,H.hydrateRoot=W.hydrateRoot;const V={position:"absolute",left:"-9999px",top:"auto",width:"1px",height:"1px",overflow:"hidden",clip:"rect(1px, 1px, 1px, 1px)",clipPath:"inset(50%)",whiteSpace:"nowrap"},Q=T.createContext(),oe=({children:t})=>{const[a,r]=s.useState(""),[d,l]=s.useState(""),o=s.useRef(null),i=s.useCallback(x=>{o.current&&clearTimeout(o.current),o.current=setTimeout(()=>{x("")},1e3)},[]),c=s.useCallback((x,u="polite")=>{!x||typeof x!="string"||(u==="assertive"?(l(x),i(l)):(r(x),i(r)))},[i]),g=s.useCallback(x=>{c(x,"polite")},[c]),n=s.useCallback(x=>{c(x,"assertive")},[c]),h={announce:c,announcePolite:g,announceAssertive:n};return e.jsxs(Q.Provider,{value:h,children:[e.jsx("div",{"aria-live":"polite","aria-atomic":"true",style:V,children:a}),e.jsx("div",{"aria-live":"assertive","aria-atomic":"true",style:V,children:d}),t]})},U=()=>{const t=s.useContext(Q);return t||(console.warn("useAnnouncer must be used within an AnnouncerProvider"),{announce:()=>{},announcePolite:()=>{},announceAssertive:()=>{}})};function ie({trigger:t,children:a,className:r="right-0 mt-2 w-48"}){const[d,l]=T.useState(!1),o=c=>{c.preventDefault(),c.stopPropagation(),l(g=>!g)};T.useEffect(()=>{const c=()=>l(!1);return window.addEventListener("click",c),()=>window.removeEventListener("click",c)},[]);const i=c=>{c.stopPropagation()};return e.jsxs("div",{className:"relative inline-block text-left profile-menu-container",onClick:o,children:[t,d&&e.jsx("div",{className:`absolute ${r} rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-50 focus:outline-none z-50`,role:"menu","aria-orientation":"vertical",tabIndex:"-1",onClick:i,children:e.jsx("div",{className:"py-1",role:"none",children:a})})]})}function le(){return e.jsx("a",{href:"#main-content",className:"sr-only focus:not(.sr-only)",children:e.jsx("span",{className:"fixed top-0 left-0 m-4 p-3 bg-indigo-600 text-white rounded z-50",children:"Skip to content"})})}function E({user:t,children:a}){var l;const[r,d]=T.useState(!1);return T.useEffect(()=>{(async()=>{let i=!1;if(t)try{const c=await fetch("/api/user/dark-mode",{credentials:"include"});c.ok&&(i=(await c.json()).dark_mode)}catch{console.log("Could not fetch dark mode preference from server")}if(!t){const c=localStorage.getItem("darkMode");i=c==="true"||c===null&&window.matchMedia("(prefers-color-scheme: dark)").matches}d(i)})()},[t]),T.useEffect(()=>{r?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode",r?"true":"false"),t&&fetch("/api/user/dark-mode",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({dark_mode:r})}).catch(o=>{console.log("Could not save dark mode preference to server:",o)})},[r,t]),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300",children:[e.jsx(le,{}),e.jsxs("header",{className:"bg-white dark:bg-gray-800 shadow-sm p-4 flex justify-between items-center transition-colors duration-300",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"w-6 h-6 text-indigo-600",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:e.jsx("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"})}),e.jsx("h1",{className:"text-xl font-bold",children:"RealHonest – get real, be real"})]}),e.jsxs("nav",{className:"space-x-4 hidden md:flex items-center",children:[e.jsx("a",{href:"/",className:"hover:text-indigo-600 transition",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"hover:text-indigo-600 transition",children:"Dashboard"}),e.jsx("a",{href:"/mentors",className:"hover:text-indigo-600 transition",children:"Mentors"}),t&&e.jsxs(ie,{trigger:e.jsxs("button",{type:"button","aria-label":"User menu",className:"focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full ml-4",children:[t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?e.jsx("img",{src:t.avatar_url,alt:"User avatar",className:"w-8 h-8 rounded-full border-2 border-indigo-500 hover:border-indigo-700 transition object-cover",onError:o=>{o.target.style.display="none",o.target.nextSibling.style.display="flex"}}):null,e.jsx("div",{className:"w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-semibold border-2 border-indigo-500 hover:border-indigo-700 transition",style:{display:t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?"none":"flex"},children:((l=t.username)==null?void 0:l.charAt(0).toUpperCase())||"?"})]}),className:"right-0 mt-2 w-48 z-50",children:[e.jsx(I,{to:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"🧾 Settings"}),e.jsx(I,{to:"/archived-chats",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📁 Archived Chats"}),e.jsx(I,{to:"/download",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📲 Download App"}),e.jsx("button",{type:"button",className:"w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-900",onClick:async()=>{if(window.confirm("Are you sure you want to delete your account?"))try{(await fetch("/api/user",{method:"DELETE",credentials:"include"})).ok?window.location.href="/logout":alert("Failed to delete account")}catch{alert("Error deleting account")}},children:"❌ Delete Account"})]}),e.jsx("button",{onClick:()=>d(!r),"aria-label":r?"Switch to light mode":"Switch to dark mode",className:"ml-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 rounded-md px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:r?"🌞 Light Mode":"🌙 Dark Mode"}),e.jsx("a",{href:"/login",className:"ml-4 hover:text-red-600 transition",children:"🚪 Login"}),e.jsx("a",{href:"/logout",className:"ml-4 hover:text-red-600 transition",children:"🚪 Logout"})]})]}),e.jsxs("main",{className:"flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6",children:[t&&e.jsx("aside",{className:"md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sticky top-6 transition-colors duration-300",children:e.jsxs("nav",{className:"space-y-2",children:[e.jsx("a",{href:"/",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📊 Dashboard"}),e.jsx("a",{href:"/saved-posts",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🔖 Saved Posts"}),e.jsx("a",{href:"/activity",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📈 Activity"}),e.jsx("a",{href:"/announcer-demo",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🔊 Accessibility Demo"}),e.jsx("a",{href:"/profile",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"⚙️ Profile"}),e.jsx("a",{href:"/chat",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"💬 Chat"}),e.jsx("a",{href:"/logout",className:"block py-2 px-3 hover:bg-red-100 dark:hover:bg-red-900 rounded text-red-600 dark:text-red-400 transition-colors",children:"🚪 Logout"})]})}),e.jsx("section",{id:"main-content",className:"flex-grow",children:a})]}),e.jsxs("footer",{className:"py-6 px-4 text-center text-gray-500 text-sm bg-white border-t mt-auto",children:["© ",new Date().getFullYear()," RealHonest – get real, be real. All rights reserved."]})]})}function de({postId:t,maxComments:a=2}){const[r,d]=s.useState([]),[l,o]=s.useState(!0),[i,c]=s.useState(0);s.useEffect(()=>{g()},[t]);const g=async()=>{try{const n=await fetch(`/api/comments/${t}`,{credentials:"include"});if(n.ok){const h=await n.json();d(h.slice(0,a)),c(h.length)}}catch(n){console.error("Error fetching comments:",n)}finally{o(!1)}};return l?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500",children:"Loading comments..."})}):r.length===0?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500 italic",children:"No comments yet. Be the first to comment!"})}):e.jsxs("div",{className:"mt-4 space-y-3",children:[r.map((n,h)=>{var x;return e.jsx("div",{className:"bg-gray-50 p-3 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"w-6 h-6 bg-indigo-400 rounded-full flex items-center justify-center text-white text-xs font-semibold",children:((x=n.author)==null?void 0:x.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:n.author||"Anonymous"}),e.jsx("span",{className:"text-xs text-gray-500",children:new Date(n.created_at).toLocaleDateString()})]}),e.jsx("p",{className:"text-sm text-gray-700 mt-1 break-words",children:n.content})]})]})},n.id||h)}),i>a&&e.jsx("div",{className:"text-center",children:e.jsxs("a",{href:`/posts/${t}#comments`,className:"text-sm text-indigo-600 hover:text-indigo-800 font-medium",children:["View all ",i," comments →"]})})]})}const ce=t=>`${window.location.origin}/posts/${t}`,me=t=>{const a=t.content.replace(/<[^>]*>/g,"").slice(0,100);return`${t.title}

${a}${a.length>=100?"...":""}`},ue=async t=>{try{return await navigator.clipboard.writeText(t),!0}catch(a){return console.error("Failed to copy to clipboard:",a),!1}},he=(t,a=120)=>`https://api.qrserver.com/v1/create-qr-code/?size=${a}x${a}&data=${encodeURIComponent(t)}`,R={whatsapp:(t,a,r)=>`https://wa.me/?text=${encodeURIComponent(`${t}

${a}

${r}`)}`,email:(t,a,r)=>`mailto:?subject=${encodeURIComponent(t)}&body=${encodeURIComponent(`${a}

Read more: ${r}`)}`,twitter:(t,a,r)=>`https://twitter.com/intent/tweet?text=${encodeURIComponent(`${t}

${a}`)}&url=${encodeURIComponent(r)}`,facebook:(t,a,r)=>`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(r)}`,linkedin:(t,a,r)=>`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(r)}`,telegram:(t,a,r)=>`https://t.me/share/url?url=${encodeURIComponent(r)}&text=${encodeURIComponent(`${t}

${a}`)}`,reddit:(t,a,r)=>`https://reddit.com/submit?url=${encodeURIComponent(r)}&title=${encodeURIComponent(t)}`,pinterest:(t,a,r)=>`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(r)}&description=${encodeURIComponent(`${t}

${a}`)}`};function B({isOpen:t,onClose:a,post:r}){const[d,l]=s.useState(!1);if(!t||!r)return null;const o=ce(r.id),i=r.title,c=me(r),g=async()=>{await ue(o)?(l(!0),setTimeout(()=>l(!1),2e3)):alert("Failed to copy link")},n=[{name:"WhatsApp",icon:"💬",color:"bg-green-500 hover:bg-green-600",url:R.whatsapp(i,c,o)},{name:"Email",icon:"📧",color:"bg-blue-500 hover:bg-blue-600",url:R.email(i,c,o)},{name:"Twitter",icon:"🐦",color:"bg-sky-500 hover:bg-sky-600",url:R.twitter(i,c,o)},{name:"Facebook",icon:"📘",color:"bg-blue-600 hover:bg-blue-700",url:R.facebook(i,c,o)},{name:"LinkedIn",icon:"💼",color:"bg-blue-700 hover:bg-blue-800",url:R.linkedin(i,c,o)},{name:"Telegram",icon:"✈️",color:"bg-blue-400 hover:bg-blue-500",url:R.telegram(i,c,o)},{name:"Reddit",icon:"🤖",color:"bg-orange-500 hover:bg-orange-600",url:R.reddit(i,c,o)},{name:"Pinterest",icon:"📌",color:"bg-red-500 hover:bg-red-600",url:R.pinterest(i,c,o)}],h=x=>{window.open(x,"_blank","width=600,height=400")};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Share Post"}),e.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 text-xl",children:"✕"})]}),e.jsxs("div",{className:"p-4 border-b bg-gray-50",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:i}),e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:c})]}),e.jsxs("div",{className:"p-4 border-b",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Copy Link"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:o,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"}),e.jsx("button",{onClick:g,className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${d?"bg-green-100 text-green-700":"bg-indigo-600 text-white hover:bg-indigo-700"}`,children:d?"✓ Copied!":"Copy"})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Share via"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:n.map(x=>e.jsxs("button",{onClick:()=>h(x.url),className:`flex items-center space-x-3 p-3 rounded-lg text-white transition-colors ${x.color}`,children:[e.jsx("span",{className:"text-lg",children:x.icon}),e.jsx("span",{className:"font-medium",children:x.name})]},x.name))})]}),e.jsxs("div",{className:"p-4 border-t bg-gray-50",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"QR Code"}),e.jsx("div",{className:"flex justify-center",children:e.jsx("img",{src:he(o,120),alt:"QR Code",className:"w-24 h-24 border rounded"})}),e.jsx("p",{className:"text-xs text-gray-500 text-center mt-2",children:"Scan to open post"})]})]})})}function xe({post:t,user:a}){var S;const[r,d]=s.useState(!1),[l,o]=s.useState({comments_count:0,user_saved:!1}),[i,c]=s.useState(!1),[g,n]=s.useState(!1),[h,x]=s.useState(""),[u,b]=s.useState(!1),[m,j]=s.useState(!1),y=A();s.useEffect(()=>{f()},[t.id]);const f=async()=>{try{const N=await fetch(`/api/posts/${t.id}/stats`,{credentials:"include"});if(N.ok){const F=await N.json();o(F)}}catch(N){console.error("Error fetching post stats:",N)}},p=async()=>{if(!a){alert("Please log in to save posts");return}c(!0);try{const N=await fetch(`/api/posts/${t.id}/save`,{method:"POST",credentials:"include"});if(N.ok){const F=await N.json();o(L=>({...L,user_saved:F.saved})),alert(F.saved?"Post saved!":"Post unsaved!")}}catch(N){console.error("Error toggling save:",N),alert("Failed to toggle save")}finally{c(!1)}},v=()=>{j(!0)},P=()=>{if(!a){alert("Please log in to comment");return}n(!g)},D=async N=>{if(N.preventDefault(),!!h.trim()){b(!0);try{const F=await fetch(`/api/comments/${t.id}`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({content:h})});if(F.ok)x(""),n(!1),f(),window.location.reload();else{const L=await F.json().catch(()=>({detail:"Failed to post comment"}));alert(`Failed to post comment: ${L.detail||"Unknown error"}`)}}catch(F){console.error("Error posting comment:",F),alert("Failed to post comment")}finally{b(!1)}}},_=t.content.replace(/<[^>]*>/g,""),k=_.length>200,w=r?t.content:_.length>200?t.content.slice(0,200)+"...":t.content;return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold",children:((S=t.author_username)==null?void 0:S.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:t.author_username||"Unknown"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(t.created_at).toLocaleDateString()})]})]}),e.jsx("h4",{className:"text-xl font-bold mb-3 text-gray-900",children:t.title}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full max-w-md mx-auto rounded-lg mb-4 cursor-pointer",onClick:()=>y(`/posts/${t.id}`)}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-gray-700 leading-relaxed prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:w}}),k&&e.jsx("button",{onClick:()=>d(!r),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2",children:r?"Show Less":"Read More"})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("button",{onClick:P,className:"flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"💬"}),e.jsx("span",{className:"text-sm font-medium",children:l.comments_count})]}),e.jsxs("button",{onClick:v,className:"flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:p,disabled:i,className:`transition-colors ${l.user_saved?"text-yellow-500 hover:text-yellow-600":"text-gray-500 hover:text-yellow-500"} ${i?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-lg",children:l.user_saved?"🔖":"📑"})}),e.jsx("button",{onClick:()=>y(`/posts/${t.id}`),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium",children:"View Full Post →"})]})]}),g&&a&&e.jsx("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:e.jsxs("form",{onSubmit:D,children:[e.jsx("textarea",{value:h,onChange:N=>x(N.target.value),placeholder:"Write a comment...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent",rows:"3",disabled:u}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-2",children:[e.jsx("button",{type:"button",onClick:()=>n(!1),className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800",disabled:u,children:"Cancel"}),e.jsx("button",{type:"submit",disabled:u||!h.trim(),className:"px-4 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Posting...":"Post Comment"})]})]})}),e.jsx(de,{postId:t.id,maxComments:2}),e.jsx(B,{isOpen:m,onClose:()=>j(!1),post:t})]})}function ge({user:t}){const[a,r]=s.useState([]),[d,l]=s.useState("Loading fact..."),o=()=>{fetch("/api/posts").then(i=>i.json()).then(r).catch(i=>console.error("Error fetching posts:",i))};return s.useEffect(()=>{o(),fetch("https://numbersapi.com/random/trivia").then(i=>i.text()).then(l).catch(i=>{console.error("Failed to load number fact",i),l("There's always something new to learn.")})},[]),s.useEffect(()=>{const i=g=>{g.key==="postsUpdated"&&(o(),localStorage.removeItem("postsUpdated"))};window.addEventListener("storage",i);const c=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(o(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",c),()=>{window.removeEventListener("storage",i),document.removeEventListener("visibilitychange",c)}},[]),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"py-16 px-4 text-center bg-gradient-to-b from-indigo-50 to-white",children:[e.jsx("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600",children:"Real Honest Conversations for Visionary Minds"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto mb-8 text-gray-800",children:"A platform for scholars, entrepreneurs, and community leaders to share authentic insights, mentor others, and build a better world without the noise of likes or dislikes."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:t?e.jsx("a",{href:"/dashboard",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition",children:"Go to Dashboard"}):e.jsxs(e.Fragment,{children:[e.jsx("a",{href:"/login",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105",children:"Join Be Real, Get Real"}),e.jsx("a",{href:"#features",className:"px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition",children:"Learn More"})]})})]}),e.jsx("section",{id:"features",className:"py-16 px-4 bg-gray-100",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-2xl font-bold text-center mb-10",children:"Core Features"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"WebRTC Chat"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Secure peer-to-peer video/audio communication between mentors and mentees."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Offline Capabilities"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Built as a Progressive Web App (PWA) — use it anywhere, even without internet access."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"No Popularity Metrics"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Focus on authentic content and meaningful discussions — no likes, no fake stories, no popularity contests."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Community Building"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Create and join topic-based communities focused on research, education, or social impact."})]})]})]})}),e.jsxs("section",{className:"py-16 px-4 text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-8 text-gray-900 dark:text-gray-100",children:"Recent Community Insights"}),a.length>0?e.jsx("div",{className:"max-w-4xl mx-auto space-y-6",children:a.map(i=>e.jsx(xe,{post:i,user:t},i.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function pe({onPostCreated:t}){const[a,r]=s.useState(""),[d,l]=s.useState(""),[o,i]=s.useState(null),[c,g]=s.useState(null),[n,h]=s.useState(!1),[x,u]=s.useState(""),[b,m]=s.useState(""),j=s.useRef(null),y=()=>{const k=document.createElement("input");k.setAttribute("type","file"),k.setAttribute("accept","image/*"),k.click(),k.onchange=async()=>{const w=k.files[0];if(w){const S=new FormData;S.append("image",w);try{const N=await fetch("/api/upload-image",{method:"POST",body:S});if(N.ok){const L=(await N.json()).url,$=j.current.getEditor(),M=$.getSelection();$.insertEmbed(M.index,"image",L)}else{const F=new FileReader;F.onload=L=>{const $=j.current.getEditor(),M=$.getSelection();$.insertEmbed(M.index,"image",L.target.result)},F.readAsDataURL(w)}}catch(N){console.error("Image upload failed:",N);const F=new FileReader;F.onload=L=>{const $=j.current.getEditor(),M=$.getSelection();$.insertEmbed(M.index,"image",L.target.result)},F.readAsDataURL(w)}}}},f=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:y}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),p=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"],v=k=>{const w=k.replace(/<[^>]*>/g,"").trim();return w?w.split(/\s+/).length:0},P=k=>{const w=v(k);return Math.ceil(w/200)},D=k=>{const w=k.target.files[0];if(w){i(w);const S=new FileReader;S.onloadend=()=>g(S.result),S.readAsDataURL(w)}},_=async k=>{if(k.preventDefault(),h(!0),u(""),m(""),!d.replace(/<[^>]*>/g,"").trim()){u("Please enter some content for your post"),h(!1);return}const S=new FormData;S.append("title",a),S.append("content",d),o&&S.append("image",o);try{const N=await fetch("/api/posts",{method:"POST",body:S,credentials:"include"});if(!N.ok){const F=await N.json().catch(()=>({detail:"Failed to create post"}));throw new Error(F.detail||"Something went wrong")}await N.json(),m("✅ Post published successfully!"),r(""),l(""),i(null),g(null),localStorage.setItem("postsUpdated",Date.now().toString()),setTimeout(t?()=>{t()}:()=>{window.location.href="/dashboard"},1e3)}catch(N){u(N.message||"❌ Error creating post")}finally{h(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded shadow-md mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"✍️ Create New Post"}),e.jsx("style",{jsx:!0,children:`
        .ql-editor {
          min-height: 250px;
          font-size: 14px;
          line-height: 1.6;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          background: #f9fafb;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: italic;
        }
        .ql-toolbar .ql-formats {
          margin-right: 8px;
        }
        .ql-toolbar button {
          padding: 4px;
          margin: 1px;
        }
        .ql-toolbar button:hover {
          background: #e5e7eb;
          border-radius: 3px;
        }
        .ql-toolbar .ql-active {
          background: #dbeafe;
          color: #1d4ed8;
          border-radius: 3px;
        }
        .ql-editor img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ql-editor blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 16px;
          margin: 16px 0;
          font-style: italic;
          color: #6b7280;
        }
        .ql-editor pre.ql-syntax {
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          padding: 12px;
          overflow-x: auto;
        }
        .ql-editor .ql-video {
          width: 100%;
          height: 315px;
        }
        .ql-snow .ql-tooltip {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .ql-snow .ql-tooltip input[type=text] {
          border: 1px solid #d1d5db;
          border-radius: 4px;
          padding: 4px 8px;
        }
        .ql-snow .ql-picker-options {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}),x&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:x}),b&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:b}),e.jsxs("form",{onSubmit:_,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:a,onChange:k=>r(k.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",d.replace(/<[^>]*>/g,"").length," characters • ",v(d)," words • ~",P(d)," min read)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(z,{ref:j,theme:"snow",value:d,onChange:l,modules:f,formats:p,placeholder:"Write your post content here... Use the enhanced toolbar for rich formatting, images, and more!",readOnly:n,style:{minHeight:"300px",backgroundColor:n?"#f3f4f6":"white"}})}),d&&e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["✨ ",e.jsx("strong",{children:"Enhanced Features:"})," Images, videos, formulas, tables, colors, fonts, and more!"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["📸 ",e.jsx("strong",{children:"Images:"})," Click the image icon to upload • 🎨 ",e.jsx("strong",{children:"Colors:"})," Highlight text and use color tools • 📐 ",e.jsx("strong",{children:"Math:"})," Use formula button for equations"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:D,disabled:n,className:"w-full px-2 py-1 border border-gray-300 rounded"})]}),c&&e.jsx("img",{src:c,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:n,className:`w-full py-2 px-4 rounded text-white font-medium transition ${n?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:n?"Publishing...":"Publish Post"})]})]})}function be({post:t,className:a="",children:r}){const[d,l]=s.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>l(!0),className:a,children:r||e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})}),e.jsx(B,{isOpen:d,onClose:()=>l(!1),post:t})]})}function fe({user:t}){const[a,r]=s.useState(null),[d,l]=s.useState([]),[o,i]=s.useState("Loading fact..."),[c,g]=s.useState(!1),n=A(),{announcePolite:h,announceAssertive:x}=U();s.useEffect(()=>{t!=null&&t.github_username&&fetch(`https://api.github.com/users/${t.github_username}`).then(m=>m.json()).then(m=>r({followers:m.followers,public_repos:m.public_repos,profile_url:m.html_url})).catch(m=>console.error("Failed to fetch GitHub data",m))},[t]);const u=()=>{fetch("/api/posts").then(m=>m.json()).then(m=>{l(m),h(`Dashboard updated with ${m.length} posts`)}).catch(m=>{console.error(m),x("Failed to load posts")})};s.useEffect(()=>{u()},[]),s.useEffect(()=>{const m=y=>{y.key==="postsUpdated"&&u()};window.addEventListener("storage",m);const j=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(u(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",j),()=>{window.removeEventListener("storage",m),document.removeEventListener("visibilitychange",j)}},[]),s.useEffect(()=>{fetch("https://numbersapi.com/random/trivia").then(m=>m.text()).then(i).catch(m=>{console.error("Failed to load number fact",m),i("There's always something new to learn.")})},[]);const b=async m=>{if(window.confirm("Are you sure you want to delete this post?"))try{const y=await fetch(`/api/posts/${m}`,{method:"DELETE",credentials:"include"});if(!y.ok){const f=await y.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(f.detail||"Something went wrong")}h("Post deleted successfully"),u()}catch(y){x(`Failed to delete post: ${y.message}`),alert(y.message)}};return!t||t.role!=="author"?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Only authors can access dashboard"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8",children:[e.jsxs("h1",{className:"text-2xl font-extrabold",children:["Welcome back, ",t.full_name||t.username]}),e.jsx("p",{className:"mt-2 opacity-90",children:"Share insights, connect, and build honest conversations."})]}),!t.mentor_profile&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-8 transition-colors duration-300",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Want to Become a Mentor?"}),e.jsx("p",{className:"mb-4 text-gray-700 dark:text-gray-300",children:"Help others grow by offering guidance in your field."}),e.jsx("a",{href:"/register-mentor",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105","aria-label":"Register as a mentor to help others grow",children:"🎓 Register as Mentor"})]}),(t==null?void 0:t.github_username)&&a&&e.jsxs("div",{className:"mt-6 bg-white dark:bg-gray-800 p-4 rounded shadow-sm transition-colors duration-300",children:[e.jsx("h2",{className:"font-semibold mb-2 text-gray-900 dark:text-gray-100",children:"GitHub Profile"}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Followers: ",a.followers]}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Repositories: ",a.public_repos]}),e.jsx("p",{children:e.jsx("a",{href:a.profile_url,target:"_blank",rel:"noopener noreferrer",className:"text-indigo-600 dark:text-indigo-400 underline",children:"View on GitHub"})})]}),e.jsx("button",{onClick:()=>g(!c),type:"button",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition mb-6","aria-label":"Create a new post",children:"✏️ New Post"}),c&&e.jsx(pe,{onPostCreated:()=>{u(),g(!1),localStorage.setItem("postsUpdated",Date.now().toString())}}),e.jsxs("section",{className:"bg-indigo-50 p-4 rounded mb-6",children:[e.jsx("h3",{className:"font-semibold",children:"Today’s Number Fact"}),e.jsx("p",{children:o})]}),e.jsxs("section",{className:"space-y-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Recent Community Posts"}),d.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(m=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:m.title}),e.jsx("div",{className:"text-gray-700 mb-4 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:m.content.length>150?m.content.slice(0,150)+"...":m.content}}),m.image_url&&e.jsx("img",{src:m.image_url,alt:m.title,className:"w-full h-48 object-cover rounded mb-4",onError:j=>{j.target.style.display="none"}}),e.jsx("small",{className:"text-sm text-gray-500",children:new Date(m.created_at).toLocaleString()}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx("a",{href:`/posts/${m.id}`,className:"text-indigo-600 hover:underline",children:"View"}),e.jsx(be,{post:m,className:"text-green-600 hover:underline",children:"📤 Share"}),e.jsx("button",{id:`edit-post-${m.id}`,onClick:()=>n(`/posts/${m.id}/edit`),"aria-label":`Edit ${m.title}`,className:"text-blue-600 hover:underline",children:"🖋️ Edit"}),e.jsx("button",{id:`delete-post-${m.id}`,onClick:()=>b(m.id),"aria-label":`Delete ${m.title}`,className:"text-red-600 hover:underline",children:"🗑️ Delete"})]})]},m.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function ye({onLogin:t}){const[a,r]=s.useState(""),[d,l]=s.useState(""),[o,i]=s.useState(""),c=async g=>{g.preventDefault();const n=new FormData;n.append("username",a),n.append("password",d);try{const h=await fetch("/login",{method:"POST",body:n,credentials:"include"});if(!h.ok){const u=await h.json();throw new Error(u.detail||"Invalid credentials")}const x=await h.json();t(x),window.location.href="/dashboard"}catch(h){i(h.message||"Failed to log in")}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Login to RealHonest"}),o&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",o]}),e.jsxs("form",{onSubmit:c,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",value:a,onChange:g=>r(g.target.value),required:!0,autoComplete:"username",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",value:d,onChange:g=>l(g.target.value),required:!0,autoComplete:"current-password",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsx("button",{type:"submit",className:"w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded",children:"Login"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx("a",{href:"/signup",className:"text-indigo-600 hover:underline",children:"Sign up"})]})]})}function je({onLogin:t}){const[a,r]=s.useState(""),[d,l]=s.useState(""),[o,i]=s.useState(""),[c,g]=s.useState(""),[n,h]=s.useState(""),[x,u]=s.useState(!1),b=async m=>{m.preventDefault(),u(!0),g(""),h("");const j=new FormData;j.append("username",a),j.append("email",d),j.append("password",o);try{const y=await fetch("/signup",{method:"POST",body:j,credentials:"include",headers:{"X-Requested-With":"XMLHttpRequest"}});if(y.redirected){window.location.href=y.url;return}if(!y.ok){const p=await y.json().catch(()=>({detail:"Signup failed"}));throw new Error(p.detail||"Something went wrong")}const f=await y.json();f.redirect?window.location.href=f.redirect:window.location.href="/profile"}catch(y){g(y.message||"Failed to register")}finally{u(!1)}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Create Your Account"}),c&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",c]}),n&&e.jsxs("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:["✅ ",n]}),e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",autoComplete:"username",value:a,onChange:m=>r(m.target.value),required:!0,disabled:x,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{id:"email",type:"email",autoComplete:"email",value:d,onChange:m=>l(m.target.value),required:!0,disabled:x,placeholder:"<EMAIL>",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",autoComplete:"new-password",value:o,onChange:m=>i(m.target.value),required:!0,disabled:x,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsx("button",{type:"submit",disabled:x,className:`w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded transition ${x?"opacity-70 cursor-not-allowed":""}`,children:x?"Creating account...":"Sign Up"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Already have an account?"," ",e.jsx("a",{href:"/login",className:"text-indigo-600 hover:underline",children:"Login"})]})]})}function ve({user:t}){const[a,r]=s.useState(""),[d,l]=s.useState("Research"),[o,i]=s.useState(""),[c,g]=s.useState(""),[n,h]=s.useState(""),[x,u]=s.useState(0),[b,m]=s.useState(""),[j,y]=s.useState(!0),f=async p=>{p.preventDefault();const v={full_name:a,category:d,expertise:o,bio:c,qualifications:n,experience_years:parseInt(x),hourly_rate:b,available:j};try{if(!(await fetch("/register-mentor",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v),credentials:"include"})).ok)throw new Error("Registration failed");alert("Successfully registered as mentor!"),window.location.href="/profile"}catch(P){console.error("Error registering:",P),alert("Failed to register mentor.")}};return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Become a Mentor"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Help others grow by offering guidance in your field."}),e.jsxs("form",{onSubmit:f,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:a,onChange:p=>r(p.target.value),required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mentor Category"}),e.jsx("select",{id:"category",value:d,onChange:p=>l(p.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none",required:!0,children:["Research","Entrepreneurship","Education","Social Impact"].map(p=>e.jsx("option",{value:p,children:p},p))})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expertise"}),e.jsx("input",{id:"expertise",type:"text",value:o,onChange:p=>i(p.target.value),required:!0,placeholder:"e.g., AI Ethics, Startup Strategy",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:c,onChange:p=>g(p.target.value),required:!0,placeholder:"What do you specialize in?",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"qualifications",className:"block text-sm font-medium text-gray-700 mb-2",children:"Qualifications"}),e.jsx("textarea",{id:"qualifications",rows:"4",value:n,onChange:p=>h(p.target.value),required:!0,placeholder:"e.g., PhD in Computer Science, 10+ years experience",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"experience_years",className:"block text-sm font-medium text-gray-700 mb-2",children:"Years of Experience"}),e.jsx("input",{id:"experience_years",type:"number",min:"0",max:"50",value:x,onChange:p=>u(parseInt(p.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"hourly_rate",className:"block text-sm font-medium text-gray-700 mb-2",children:"Hourly Rate"}),e.jsx("input",{id:"hourly_rate",type:"text",value:b,onChange:p=>m(p.target.value),placeholder:"e.g., $50/hour",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:j,onChange:()=>y(!j),className:"mr-2"}),e.jsx("label",{htmlFor:"available",className:"text-sm text-gray-700",children:"I am available for mentoring sessions"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",className:"px-5 py-2 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-md shadow transition",children:"Register as Mentor"})})]})]})})}function we({user:t}){const{mentorId:a}=q(),[r,d]=s.useState(null),[l,o]=s.useState(!0);return s.useEffect(()=>{fetch(`/api/mentors/${a}`).then(i=>i.json()).then(d).finally(()=>o(!1))},[a]),l?e.jsx("p",{children:"Loading mentor profile..."}):r?e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:r.full_name}),e.jsxs("p",{className:"text-indigo-600 font-medium mt-1",children:["Category: ",r.category]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Expertise:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.expertise})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Bio:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.bio||"No bio provided."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Qualifications:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.qualifications||"Not specified."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Experience:"}),e.jsxs("p",{className:"mt-2 text-gray-700",children:[r.experience_years," years"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Hourly Rate:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:r.hourly_rate})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:`/dm/${r.user.username}`,className:"px-4 py-2 bg-indigo-600 text-white rounded-md transition",children:"💬 Message Mentor"}),(t==null?void 0:t.role)==="author"&&e.jsx("a",{href:`/edit-mentor/${r.id}`,className:"px-4 py-2 bg-green-600 text-white rounded-md transition",children:"✏️ Edit Profile"})]})]})}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Mentor not found"}),e.jsx("a",{href:"/mentors",className:"text-indigo-600 underline",children:"Browse All Mentors"})]})}function Ne({user:t}){const[a,r]=s.useState([]),[d,l]=s.useState(""),o=s.useRef(null),i=s.useRef(null);s.useEffect(()=>{var h;if(!t)return;const n=(h=document.cookie.split("; ").find(x=>x.startsWith("access_token=")))==null?void 0:h.split("=")[1];i.current=new WebSocket("ws://localhost:8000/ws/chat"),i.current.onopen=()=>{i.current.send(JSON.stringify({token:n}))},i.current.onmessage=x=>{try{const u=JSON.parse(x.data);r(b=>[...b,u])}catch{console.error("Failed to parse message:",x.data)}}},[t]);const c=n=>{var x;if(n.preventDefault(),!d.trim())return;if(g(d)){alert("Your message contains restricted content.");return}const h={sender:(t==null?void 0:t.username)||"Anonymous",text:d,timestamp:new Date().toISOString()};((x=i.current)==null?void 0:x.readyState)===WebSocket.OPEN?i.current.send(JSON.stringify(h)):console.warn("WebSocket not connected"),r(u=>[...u,h]),l("")},g=n=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(x=>n.toLowerCase().includes(x));return s.useEffect(()=>{o.current&&(o.current.scrollTop=o.current.scrollHeight)},[a]),t?e.jsxs("main",{className:"max-w-4xl mx-auto p-6",children:[e.jsxs("section",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Video Chat with Mentor"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"localVideo",autoPlay:!0,muted:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"You"})]}),e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"remoteVideo",autoPlay:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"Remote"})]})]}),e.jsxs("div",{className:"mt-4 flex justify-center space-x-4",children:[e.jsx("button",{id:"startCallBtn",type:"button","aria-label":"Start video call with mentor",className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-md transition",children:"Start Call"}),e.jsx("button",{id:"endCallBtn",type:"button","aria-label":"End video call",className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md shadow-md transition",children:"End Call"})]})]}),e.jsxs("section",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Chat Messages"}),e.jsx("div",{id:"messageList",ref:o,className:"space-y-4 max-h-96 overflow-y-auto p-2 border border-gray-200 rounded",children:a.length>0?a.map((n,h)=>e.jsx("div",{className:`py-2 ${n.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded-lg ${n.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:n.sender}),": ",n.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(n.timestamp).toLocaleTimeString()})]})},h)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{id:"chatForm",onSubmit:c,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{id:"messageInput",type:"text",value:d,onChange:n=>l(n.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition",children:"Send"})]})]})]}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Please log in to use the chat"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]})}function ke({user:t}){const[a,r]=s.useState([]),[d,l]=s.useState(""),o=s.useRef(null);s.useEffect(()=>{const g=new WebSocket(`ws://localhost:8000/ws/dm/${t.username}?token=Bearer%20${localStorage.getItem("access_token")}`);g.onmessage=n=>{try{const h=JSON.parse(n.data);r(x=>[...x,h])}catch{console.error("Failed to parse:",n.data)}}},[t]);const i=g=>{if(g.preventDefault(),!d.trim())return;if(c(d)){alert("Your message contains restricted words");return}const n={sender:t.username,receiver:t.username,text:d,timestamp:new Date().toISOString()};ws.readyState===WebSocket.OPEN&&ws.send(JSON.stringify(n)),r(h=>[...h,n]),l("")},c=g=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(h=>g.toLowerCase().includes(h));return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("section",{className:"bg-white p-6 rounded shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Message Allen"}),e.jsx("div",{ref:o,className:"space-y-3 max-h-96 overflow-y-auto p-4 border border-gray-200 rounded mb-4",children:a.length>0?a.map((g,n)=>e.jsx("div",{className:`py-2 ${g.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded ${g.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:g.sender}),": ",g.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(g.timestamp).toLocaleTimeString()})]})},n)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{onSubmit:i,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{type:"text",value:d,onChange:g=>l(g.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700",children:"Send"})]})]})})}function Se(){const[t,a]=s.useState([]),[r,d]=s.useState(""),[l,o]=s.useState(!0),[i,c]=s.useState(null);s.useEffect(()=>{async function n(){try{const h=await fetch("/api/mentors");if(!h.ok)throw new Error("Failed to load mentors");const x=await h.json();a(x)}catch(h){c(h.message)}finally{o(!1)}}n()},[]);const g=async()=>{if(r.trim())try{const n=await fetch(`/api/mentors/country?country=${r}`);if(!n.ok)throw new Error("No mentors found");const h=await n.json();a(h.mentors||[])}catch{alert("No mentors found for that country.")}};return l?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading mentors..."})}):i?e.jsxs("div",{className:"text-red-500 text-center py-10",children:[e.jsx("p",{children:i}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 underline",children:"Try again"})]}):e.jsxs("main",{className:"max-w-6xl mx-auto p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Find a Mentor"}),e.jsxs("form",{onSubmit:n=>{n.preventDefault(),g()},className:"mb-8 flex gap-2 max-w-md mx-auto",children:[e.jsx("input",{type:"text",value:r,onChange:n=>d(n.target.value),placeholder:"Search mentors by country...",className:"w-full px-4 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"🔍"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.length>0?t.map((n,h)=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition",children:[e.jsx("h3",{className:"text-xl font-semibold",children:n.full_name}),e.jsx("p",{className:"text-indigo-600 font-medium mt-1",children:n.category}),e.jsx("p",{className:"mt-2 text-gray-700 line-clamp-2",children:n.expertise}),e.jsxs("p",{className:"mt-2 text-sm text-gray-500",children:["Experience: ",n.experience_years," years"]}),e.jsx(I,{to:`/mentors/${n.id}`,className:"mt-4 inline-block text-indigo-600 hover:text-indigo-800 hover:underline",children:"View Profile"})]},h)):e.jsx("p",{className:"italic text-gray-500 col-span-full text-center py-10",children:"No mentors found. Try searching by country or check back later."})})]})}function X({postId:t}){const[a,r]=s.useState([]),[d,l]=s.useState(""),[o,i]=s.useState(1),[c,g]=s.useState(!0),[n,h]=s.useState(!1),x=async m=>{const y=await(await fetch(`/api/comments/${t}?page=${m}`)).json();y.length===0&&g(!1),r(f=>[...f,...y])};s.useEffect(()=>{x(o)},[o]);const u=()=>{c&&!n&&i(m=>m+1)},b=async m=>{m.preventDefault();try{const j=await fetch(`/api/comments/${t}`,{method:"POST",body:JSON.stringify({content:d}),headers:{"Content-Type":"application/json"},credentials:"include"});if(j.ok){const y=await j.json();r([y,...a]),l("")}else{const y=await j.json().catch(()=>({detail:"Failed to submit comment"}));alert(`Failed to submit comment: ${y.detail||"Unknown error"}`)}}catch(j){console.error("Error submitting comment:",j),alert("Failed to submit comment: Network error")}};return e.jsxs("section",{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Comments"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[a.map(m=>e.jsxs("div",{className:"bg-gray-50 p-4 rounded border",children:[e.jsx("p",{className:"text-gray-800",children:m.content}),e.jsxs("small",{className:"text-sm text-gray-500",children:["— ",m.author," • ",new Date(m.created_at).toLocaleString()]})]},m.id)),c&&e.jsx("button",{onClick:u,className:"text-indigo-600 hover:text-indigo-800 underline text-sm",children:"Load More Comments"})]}),e.jsxs("form",{onSubmit:b,className:"space-y-4",children:[e.jsx("textarea",{value:d,onChange:m=>l(m.target.value),placeholder:"Add a comment...",className:"w-full px-4 py-2 border border-gray-300 rounded",required:!0}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded",children:"Submit"})]})]})}function Ce({user:t}){const{id:a}=q(),[r,d]=s.useState(null),[l,o]=s.useState(!1),[i,c]=s.useState(!1),g=A();s.useEffect(()=>{fetch(`/api/posts/${a}`).then(u=>u.json()).then(d).catch(u=>console.error("Error fetching post:",u))},[a]);const n=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${a}`,{method:"DELETE",credentials:"include"});if(!b.ok){const m=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(m.detail||"Something went wrong")}alert("Post deleted successfully!"),g("/dashboard")}catch(b){alert(b.message)}},h=()=>{g(`/posts/${a}/edit`)},x=async()=>{if(!t){alert("Please log in to save posts");return}c(!0);try{const u=await fetch(`/api/posts/${r.id}/save`,{method:"POST",credentials:"include"});if(u.ok){const b=await u.json();d(m=>({...m,user_saved:b.saved})),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(u){console.error("Error toggling save:",u),alert("Failed to toggle save")}finally{c(!1)}};return r?e.jsx("main",{role:"main",id:"main-content",className:"max-w-3l mx auto",children:e.jsxs("div",{className:"max-w-3xl mx-auto p-6 bg-white rounded shadow",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:r.title}),r.image_url&&e.jsx("img",{src:r.image_url,alt:r.title,className:"w-full h-auto rounded mb-4"}),e.jsx("div",{className:"mb-6 prose prose-lg max-w-none",dangerouslySetInnerHTML:{__html:r.content}}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-6",children:["By ",r.author_username," • ",new Date(r.created_at).toLocaleString()]}),e.jsxs("div",{className:"flex gap-4 mb-6",children:[t&&e.jsx("button",{onClick:x,disabled:i,className:`px-4 py-2 rounded transition ${r.user_saved?"bg-indigo-600 hover:bg-indigo-700 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"} ${i?"opacity-50 cursor-not-allowed":""}`,children:i?"⏳":r.user_saved?"🔖 Saved":"📌 Save Post"}),e.jsx("button",{onClick:()=>o(!0),className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"📤 Share Post"}),t&&(t.username===r.author_username||t.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:h,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition",children:"🖋️ Edit Post"}),e.jsx("button",{onClick:n,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition",children:"🗑️ Delete Post"})]})]}),e.jsx(X,{postId:r.id}),e.jsx(B,{isOpen:l,onClose:()=>o(!1),post:r})]})}):e.jsx("p",{children:"Loading..."})}function Ee({post:t,user:a,showActions:r=!1,onPostUpdate:d}){const l=A(),[o,i]=s.useState(!1),[c,g]=s.useState(t),n=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${t.id}`,{method:"DELETE",credentials:"include"});if(!b.ok){const m=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(m.detail||"Something went wrong")}alert("Post deleted successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),window.location.reload()}catch(b){alert(b.message)}},h=()=>{l(`/posts/${t.id}/edit`)},x=async()=>{if(!a){alert("Please log in to save posts");return}i(!0);try{const u=await fetch(`/api/posts/${c.id}/save`,{method:"POST",credentials:"include"});if(u.ok){const b=await u.json();g(m=>({...m,user_saved:b.saved})),d&&d(c.id,{user_saved:b.saved}),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(u){console.error("Error toggling save:",u),alert("Failed to toggle save")}finally{i(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:t.title}),e.jsx("div",{className:"text-gray-700 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:t.content.length>200?t.content.slice(0,200)+"...":t.content}}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full h-40 object-cover rounded mb-4"}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-3",children:["By ",c.author_username," • ",new Date(c.created_at).toLocaleDateString()]}),e.jsxs("div",{className:"flex gap-2 items-center flex-wrap",children:[e.jsx("a",{href:`/posts/${c.id}`,className:"text-indigo-600 hover:underline",children:"View Post"}),a&&e.jsx("button",{onClick:x,disabled:o,className:`text-sm px-2 py-1 rounded transition ${c.user_saved?"text-indigo-600 bg-indigo-50":"text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"} ${o?"opacity-50 cursor-not-allowed":""}`,children:o?"⏳":c.user_saved?"🔖 Saved":"📌 Save"}),r&&a&&(a.username===c.author_username||a.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:h,className:"text-blue-600 hover:underline text-sm",children:"🖋️ Edit"}),e.jsx("button",{onClick:n,className:"text-red-600 hover:underline text-sm",children:"🗑️ Delete"})]})]})]})}function Pe({initialPosts:t=[]}){const[a,r]=s.useState(t),[d,l]=s.useState(!0),[o,i]=s.useState(1),[c,g]=s.useState(!1),[n,h]=s.useState(""),[x,u]=s.useState(""),[b,m]=s.useState("latest"),j=async()=>{if(!(!d||c)){g(!0);try{const f=await(await fetch(`/api/posts?page=${o}&limit=6`)).json();f.length===0&&l(!1),r(p=>[...p,...f]),i(p=>p+1)}catch{console.error("Failed to load more posts")}finally{g(!1)}}};return useEffect(()=>{const y=()=>{window.innerHeight+window.scrollY>=document.body.offsetHeight-500&&d&&!c&&j()};return window.addEventListener("scroll",y),()=>window.removeEventListener("scroll",y)},[d,c]),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(y=>e.jsx(Ee,{post:y},y.id))}),c&&e.jsx("p",{className:"text-center",children:"Loading more posts..."}),!d&&e.jsx("p",{className:"text-center text-gray-500 italic",children:"No more posts to load"})]})}function Fe({user:t}){const[a,r]=s.useState([]),[d,l]=s.useState(!0),[o,i]=s.useState(null),{announcePolite:c,announceAssertive:g}=U();return s.useEffect(()=>{if(!(t!=null&&t.username)){l(!1);return}l(!0),i(null),fetch(`/api/activity/${t.username}`).then(n=>{if(!n.ok)throw new Error("Failed to fetch activities");return n.json()}).then(n=>{r(n||[]),l(!1),c(`Activity feed loaded with ${(n==null?void 0:n.length)||0} activities`)}).catch(n=>{console.error("Error fetching activities:",n),i(n.message),r([]),l(!1),g("Failed to load activity feed")})},[t]),t?d?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsx("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(n=>e.jsx("div",{className:"bg-gray-200 h-16 rounded"},n))})]})}):o?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading activities: ",o]}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),a.length>0?e.jsx("div",{className:"space-y-4",children:a.map((n,h)=>e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-indigo-500 hover:shadow-md transition-shadow",children:[e.jsx("p",{className:"text-gray-900 dark:text-gray-100 font-medium",children:n.action}),e.jsx("small",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(n.timestamp).toLocaleString()})]},h))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"No activity yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Start creating posts and interacting to see your activity here!"}),e.jsx("a",{href:"/dashboard",className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Go to Dashboard"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your activity feed."})})})}function _e({user:t}){const[a,r]=s.useState([]),[d,l]=s.useState(!0),[o,i]=s.useState(null),c=A(),{announcePolite:g,announceAssertive:n}=U();s.useEffect(()=>{if(!t){l(!1);return}h()},[t]);const h=async()=>{try{l(!0),i(null);const u=await fetch("/api/saved-posts",{credentials:"include"});if(!u.ok)throw new Error("Failed to load saved posts");const b=await u.json();r(b),g(`Loaded ${b.length} saved posts`)}catch(u){console.error("Error fetching saved posts:",u),i(u.message),n("Failed to load saved posts")}finally{l(!1)}},x=async u=>{try{if((await fetch(`/api/posts/${u}/save`,{method:"POST",credentials:"include"})).ok)r(m=>m.filter(j=>j.id!==u)),g("Post removed from saved posts");else throw new Error("Failed to unsave post")}catch(b){console.error("Error unsaving post:",b),n("Failed to remove post from saved posts"),alert("Failed to unsave post")}};return t?d?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),e.jsx("div",{className:"animate-pulse space-y-6",children:[1,2,3].map(u=>e.jsx("div",{className:"bg-gray-200 h-32 rounded"},u))})]})}):o?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading saved posts: ",o]}),e.jsx("button",{onClick:h,className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Saved Posts"}),a.length>0?e.jsx("div",{className:"space-y-6",children:a.map(u=>e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-gray-100",children:u.title}),e.jsx("button",{onClick:()=>x(u.id),className:"text-indigo-600 hover:text-red-600 transition-colors p-2 rounded hover:bg-red-50",title:"Remove from saved",children:"🔖"})]}),u.image_url&&e.jsx("img",{src:u.image_url,alt:u.title,className:"w-full h-48 object-cover rounded mb-4"}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 mb-4 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:u.content.length>200?u.content.slice(0,200)+"...":u.content}}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{children:["By ",u.author_username," • ",new Date(u.created_at).toLocaleDateString()]}),e.jsxs("span",{children:["Saved ",new Date(u.saved_at).toLocaleDateString()]})]}),e.jsx("div",{className:"mt-4 flex gap-2",children:e.jsx("button",{onClick:()=>c(`/posts/${u.id}`),className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"Read Full Post"})})]},u.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📌"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"No saved posts yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Start saving posts you want to read later!"}),e.jsx("button",{onClick:()=>c("/dashboard"),className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Browse Posts"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your saved posts."})})})}function De({postId:t}){const[a,r]=s.useState(""),[d,l]=s.useState(""),[o,i]=s.useState(null),[c,g]=s.useState(null),[n,h]=s.useState(!1),[x,u]=s.useState(""),{id:b}=q(),m=A();s.useEffect(()=>{fetch(`/api/posts/${t}`).then(f=>f.json()).then(f=>{r(f.title),l(f.content),g(f.image_url)})},[t]);const j=f=>{const p=f.target.files[0];if(p){i(p);const v=new FileReader;v.onloadend=()=>g(v.result),v.readAsDataURL(p)}},y=async f=>{f.preventDefault(),h(!0),u(""),m(`/posts/${b}`);const p=new FormData;p.append("title",a),p.append("content",d),o&&p.append("image",o);try{if(!(await fetch(`/api/posts/${t}`,{method:"POST",body:p,credentials:"include"})).ok)throw new Error("Update failed");alert("Post updated!"),window.location.href=`/posts/${t}`}catch(v){u(v.message||"Error updating post")}finally{h(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Edit Your Post"}),x&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:x}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:a,onChange:f=>r(f.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:"Content"}),e.jsx("textarea",{id:"content",rows:"5",value:d,onChange:f=>l(f.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Upload New Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:j,disabled:n,className:"w-full px-2 py-1 border border-gray-300 rounded disabled:bg-gray-100"})]}),c&&e.jsx("img",{src:c,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:n,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${n?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:n?"Saving...":"Save Changes"})]})]})}function Le(){const[t,a]=s.useState(0),[r,d]=s.useState(""),{announcePolite:l,announceAssertive:o}=U(),i=()=>{const h=r||`Button clicked ${t+1} times`;l(h),a(x=>x+1)},c=()=>{o(r||"This is an urgent announcement!")},g=h=>{h.preventDefault(),l("Form submitted successfully")},n=()=>{o("Error: Something went wrong!")};return e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Announcer Demo - Screen Reader Accessibility"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900 p-4 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"What is the Announcer?"}),e.jsx("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:"The Announcer component provides accessibility announcements for screen readers. It uses ARIA live regions to communicate dynamic content changes to visually impaired users. The announcements are invisible but will be read aloud by screen readers."})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"custom-message",className:"block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300",children:"Custom Message (optional):"}),e.jsx("input",{id:"custom-message",type:"text",value:r,onChange:h=>d(h.target.value),placeholder:"Enter a custom message to announce...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Polite Announcements"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"These don't interrupt current screen reader speech"}),e.jsxs("button",{onClick:i,className:"w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:["🔊 Polite Announcement (Counter: ",t,")"]}),e.jsx("button",{onClick:()=>l("Page content has been updated"),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition",children:"📄 Content Updated"}),e.jsx("button",{onClick:()=>l("New post has been added to the feed"),className:"w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"➕ New Content Added"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Assertive Announcements"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"These interrupt current speech for urgent messages"}),e.jsx("button",{onClick:c,className:"w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition",children:"⚠️ Urgent Announcement"}),e.jsx("button",{onClick:n,className:"w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition",children:"🚨 Error Announcement"}),e.jsx("button",{onClick:()=>o("Form validation failed. Please check your inputs."),className:"w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition",children:"❌ Validation Error"})]})]}),e.jsxs("div",{className:"border-t pt-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Form Interaction Demo"}),e.jsxs("form",{onSubmit:g,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"demo-input",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Demo Input:"}),e.jsx("input",{id:"demo-input",type:"text",placeholder:"Type something...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),e.jsx("button",{type:"submit",className:"px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"Submit Form (Announces Success)"})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"How to Test:"}),e.jsxs("ul",{className:"text-sm text-gray-700 dark:text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Turn on a screen reader (NVDA, JAWS, VoiceOver, etc.)"}),e.jsx("li",{children:"• Click the buttons above"}),e.jsx("li",{children:"• Listen for the announcements"}),e.jsx("li",{children:"• Polite announcements wait for current speech to finish"}),e.jsx("li",{children:"• Assertive announcements interrupt current speech"})]})]})]})]})})}function $e({user:t}){const{postId:a}=q(),[r,d]=s.useState(null),[l,o]=s.useState(""),[i,c]=s.useState(""),[g,n]=s.useState(null),[h,x]=s.useState(!0),u=A(),b=s.useRef(null),m=()=>{const p=document.createElement("input");p.setAttribute("type","file"),p.setAttribute("accept","image/*"),p.click(),p.onchange=async()=>{const v=p.files[0];if(v){const P=new FormData;P.append("image",v);try{const D=await fetch("/api/upload-image",{method:"POST",body:P});if(D.ok){const k=(await D.json()).url,w=b.current.getEditor(),S=w.getSelection();w.insertEmbed(S.index,"image",k)}else{const _=new FileReader;_.onload=k=>{const w=b.current.getEditor(),S=w.getSelection();w.insertEmbed(S.index,"image",k.target.result)},_.readAsDataURL(v)}}catch(D){console.error("Image upload failed:",D);const _=new FileReader;_.onload=k=>{const w=b.current.getEditor(),S=w.getSelection();w.insertEmbed(S.index,"image",k.target.result)},_.readAsDataURL(v)}}}},j=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:m}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),y=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"];s.useEffect(()=>{async function p(){const v=await fetch(`/api/posts/${a}`,{credentials:"include"});if(!v.ok){u("/dashboard");return}const P=await v.json();if(P.author_username!==(t==null?void 0:t.username)&&(t==null?void 0:t.role)!=="admin"){u("/dashboard");return}o(P.title),c(P.content),d(P),x(!1)}t&&p()},[a,t,u]);const f=async p=>{p.preventDefault();const v=new FormData;v.append("title",l),v.append("content",i),g&&v.append("post_image",g);try{const P=await fetch(`/api/posts/${a}`,{method:"POST",body:v,credentials:"include"});if(P.ok)alert("Post updated successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),u(`/posts/${a}`);else{const D=await P.json().catch(()=>({detail:"Failed to update post"}));alert(`Failed to update post: ${D.detail||"Unknown error"}`)}}catch(P){console.error("Error updating post:",P),alert(`Network error – could not update post: ${P.message}`)}};return h?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading post..."})}):e.jsxs("main",{className:"container mx-auto py-10 px-4 max-w-3xl",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Edit Your Post"}),e.jsxs("form",{onSubmit:f,encType:"multipart/form-data",className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",name:"title",value:l,onChange:p=>o(p.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",i.replace(/<[^>]*>/g,"").length," characters)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(z,{ref:b,theme:"snow",value:i,onChange:c,modules:j,formats:y,placeholder:"Edit your post content here...",style:{minHeight:"300px",backgroundColor:"white"}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"post_image",className:"block text-sm font-medium text-gray-700 mb-1",children:"Update Image (Optional)"}),e.jsx("input",{id:"post_image",type:"file",name:"post_image",accept:"image/*",onChange:p=>n(p.target.files[0]),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md transition",children:"Update Post"})]})]})}function Re(){const[t,a]=s.useState(""),[r,d]=s.useState(""),[l,o]=s.useState(""),[i,c]=s.useState("/static/avatars/default.png"),[g,n]=s.useState(!1),h=u=>{const b=u.target.files[0];if(b){const m=new FileReader;m.onload=()=>c(m.result),m.readAsDataURL(b)}},x=async u=>{u.preventDefault(),n(!0);const b=new FormData;b.append("full_name",t),b.append("bio",r),b.append("github_username",l),u.target.avatar.files.length>0&&b.append("avatar",u.target.avatar.files[0]);try{console.log("Submitting profile data:",{fullName:t,bio:r,github:l});const m=await fetch("/create-profile",{method:"POST",credentials:"include",body:b});if(console.log("Response status:",m.status),m.ok)console.log("Profile created successfully"),alert("Profile saved successfully!"),window.location.href="/dashboard";else{const j=await m.text();console.error("Profile creation failed:",j),alert(`Update failed: ${j}`)}}catch(m){console.error("Profile creation error:",m),alert(`Error updating profile: ${m.message}`)}finally{n(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),e.jsxs("form",{onSubmit:x,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{type:"text",value:t,onChange:u=>a(u.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{rows:"4",value:r,onChange:u=>d(u.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"GitHub Username"}),e.jsx("input",{type:"text",value:l,onChange:u=>o(u.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:i,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:h,accept:"image/*"})]}),e.jsx("button",{type:"submit",className:"mt-4 w-full py-2 px-4 rounded bg-indigo-600 hover:bg-indigo-700 text-white",disabled:g,children:g?"Saving...":"Save Profile"})]})]})}function Ae({user:t}){const[a,r]=s.useState((t==null?void 0:t.full_name)||""),[d,l]=s.useState((t==null?void 0:t.bio)||""),[o,i]=s.useState((t==null?void 0:t.github_username)||""),[c,g]=s.useState((t==null?void 0:t.avatar_url)||"/static/avatars/default.png"),[n,h]=s.useState(!1),[x,u]=s.useState(!1),[b,m]=s.useState(""),j=f=>{const p=f.target.files[0];if(p){const v=new FileReader;v.onload=()=>g(v.result),v.readAsDataURL(p)}},y=async f=>{f.preventDefault(),u(!0),m(""),h(!1);const p=new FormData;p.append("full_name",a),p.append("bio",d),p.append("github_username",o),image&&p.append("image",image),f.target.avatar.files.length>0&&p.append("avatar",f.target.avatar.files[0]);try{if(!(await fetch("/create-profile",{method:"POST",credentials:"include",body:p})).ok)throw new Error("Failed to update profile");h(!0),setTimeout(()=>{window.location.href="/dashboard"},1e3)}catch(v){m(v.message||"Error updating profile")}finally{u(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),n&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded",children:"✅ Profile updated successfully!"}),b&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:["❌ ",b]}),e.jsxs("form",{onSubmit:y,className:"space-y-4",children:[e.jsx("form",{action:"/create-profile",method:"post",enctype:"multipart/form-data"}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:a,onChange:f=>r(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:d,onChange:f=>l(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"github_username",className:"block text-sm font-medium mb-1",children:"GitHub Username (optional)"}),e.jsx("input",{id:"github_username",type:"text",value:o,onChange:f=>i(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:x})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:c,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:j,accept:"image/*",disabled:x,ImageUpload:!0,onUpload:f=>setImage(f),className:"w-full px-2 py-1 border rounded"})]}),e.jsx("button",{type:"submit",disabled:x,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${x?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:x?"Saving Changes...":"Save Profile"})]})]})}function Te({user:t}){return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"No archived chats yet."}),e.jsx("p",{children:"You can view past chat logs here once they are implemented."})]})})}function Me({user:t}){const[a,r]=s.useState(!1),[d,l]=s.useState(null);s.useEffect(()=>{const i=c=>{c.preventDefault(),l(c),r(!0)};return window.addEventListener("beforeinstallprompt",i),r(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone===!0||window.matchMedia("(prefers-app-theme: dark)").matches),()=>window.removeEventListener("beforeinstallprompt",i)},[]);const o=i=>{i.preventDefault(),d&&d.prompt&&d.prompt()};return e.jsxs("main",{className:"max-w-xl mx-auto p-6 bg-white rounded shadow-md mt-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Download RealHonest"}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Install as Web App"}),e.jsx("p",{className:"mb-4 text-gray-700",children:"RealHonest works great on all devices. You can install it like a native app."}),a?e.jsx("button",{type:"button",onClick:o,className:"w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"💾 Install App"}):e.jsx("div",{className:"bg-blue-50 text-blue-700 p-3 rounded",children:e.jsx("p",{className:"text-sm",children:'To install, tap the browser menu and select "Add to Home Screen" or use the desktop app install option.'})})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📲 Native Mobile App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"RealHonest will soon be available on iOS and Android."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white text-center rounded transition",children:"📱 Download for iOS (Coming Soon)"}),e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white text-center rounded transition",children:"📱 Download for Android (Coming Soon)"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"💻 Desktop App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"Use RealHonest on desktop platforms today."}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Windows"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"macOS"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Linux"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Scan to Access"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Use this QR code to easily share the app with others."}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:"https://quickchart.io/qr?text=http://localhost:5173&size=150",alt:"QR Code for RealHonest",className:"w-40 h-40 border p-2 rounded bg-white shadow-md"})})]}),e.jsx("div",{className:"text-center",children:e.jsx("a",{href:"/profile",className:"inline-block text-indigo-600 hover:underline",children:"← Back to Profile"})})]})}function Ie(){const[t,a]=s.useState(null),[r,d]=s.useState(!0);return s.useEffect(()=>{fetch("/api/user",{credentials:"include"}).then(l=>{if(l.ok)return l.json();throw new Error("Not authenticated")}).then(l=>a(l)).catch(()=>a(null)).finally(()=>d(!1))},[]),r?e.jsx("div",{className:"flex justify-center items-center h-screen bg-gray-50",children:e.jsx("p",{children:"Loading..."})}):e.jsx(oe,{children:e.jsx(Y,{children:e.jsxs(Z,{children:[e.jsx(C,{path:"/",element:e.jsx(E,{user:t,children:e.jsx(ge,{user:t})})}),e.jsx(C,{path:"/dashboard",element:e.jsx(E,{user:t,children:e.jsx(fe,{user:t})})}),e.jsx(C,{path:"/login",element:e.jsx(E,{user:t,children:e.jsx(ye,{onLogin:a})})}),e.jsx(C,{path:"/signup",element:e.jsx(E,{user:t,children:e.jsx(je,{onLogin:a})})}),e.jsx(C,{path:"/posts/:id",element:e.jsx(E,{user:t,children:e.jsx(Ce,{user:t})})}),e.jsx(C,{path:"/posts/:postId/edit",element:e.jsx(E,{user:t,children:e.jsx($e,{user:t})})}),e.jsx(C,{path:"/register-mentor",element:e.jsx(E,{user:t,children:e.jsx(ve,{user:t})})}),e.jsx(C,{path:"/mentors/:mentorId",element:e.jsx(E,{user:t,children:e.jsx(we,{user:t})})}),e.jsx(C,{path:"/mentors",element:e.jsx(E,{user:t,children:e.jsx(Se,{user:t})})}),e.jsx(C,{path:"/chat",element:e.jsx(E,{user:t,children:e.jsx(Ne,{user:t})})}),e.jsx(C,{path:"/dm/:username",element:e.jsx(E,{user:t,children:e.jsx(ke,{user:t})})}),e.jsx(C,{path:"/profile",element:e.jsx(E,{user:t,children:e.jsx(Re,{user:t})})}),e.jsx(C,{path:"/activity",element:e.jsx(E,{user:t,children:e.jsx(Fe,{user:t})})}),e.jsx(C,{path:"/saved-posts",element:e.jsx(E,{user:t,children:e.jsx(_e,{user:t})})}),e.jsx(C,{path:"/announcer-demo",element:e.jsx(E,{user:t,children:e.jsx(Le,{})})}),e.jsx(C,{path:"/posts",elements:e.jsx(E,{user:t,children:e.jsx(Pe,{user:t})})}),e.jsx(C,{path:"/editpostform",element:e.jsx(E,{user:t,children:e.jsx(De,{})})}),e.jsx(C,{path:"/profile",element:e.jsx(E,{children:e.jsx(Ae,{user:t})})}),e.jsx(C,{path:"/archived-chats",element:e.jsx(E,{children:e.jsx(Te,{user:t})})}),e.jsx(C,{path:"/download",element:e.jsx(E,{children:e.jsx(Me,{user:t})})}),e.jsx(C,{path:"/comments",element:e.jsx(E,{children:e.jsx(X,{user:t})})})]})})})}const qe=H.createRoot(document.getElementById("root"));qe.render(e.jsx(Y,{children:e.jsx(Ie,{})}));"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(t=>console.log("Service Worker registered:",t.scope)).catch(t=>console.error("Service Worker registration failed:",t))});
