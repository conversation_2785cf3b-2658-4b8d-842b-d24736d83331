import{r as s,a as X,b as I}from"./vendor-DXBhkOeJ.js";import{L as T,u as R,a as q,b as Z,R as ee,c as F,B as te}from"./router-BGt2mvSm.js";import{R as Y}from"./quill-C38wxQcs.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))d(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const l of o.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&d(l)}).observe(document,{childList:!0,subtree:!0});function a(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function d(i){if(i.ep)return;i.ep=!0;const o=a(i);fetch(i.href,o)}})();var G={exports:{}},O={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var se=s,ae=Symbol.for("react.element"),re=Symbol.for("react.fragment"),ne=Object.prototype.hasOwnProperty,oe=se.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ie={key:!0,ref:!0,__self:!0,__source:!0};function J(t,r,a){var d,i={},o=null,l=null;a!==void 0&&(o=""+a),r.key!==void 0&&(o=""+r.key),r.ref!==void 0&&(l=r.ref);for(d in r)ne.call(r,d)&&!ie.hasOwnProperty(d)&&(i[d]=r[d]);if(t&&t.defaultProps)for(d in r=t.defaultProps,r)i[d]===void 0&&(i[d]=r[d]);return{$$typeof:ae,type:t,key:o,ref:l,props:i,_owner:oe.current}}O.Fragment=re;O.jsx=J;O.jsxs=J;G.exports=O;var e=G.exports,B={},V=X;B.createRoot=V.createRoot,B.hydrateRoot=V.hydrateRoot;const z={position:"absolute",left:"-9999px",top:"auto",width:"1px",height:"1px",overflow:"hidden",clip:"rect(1px, 1px, 1px, 1px)",clipPath:"inset(50%)",whiteSpace:"nowrap"},Q=I.createContext(),le=({children:t})=>{const[r,a]=s.useState(""),[d,i]=s.useState(""),o=s.useRef(null),l=s.useCallback(u=>{o.current&&clearTimeout(o.current),o.current=setTimeout(()=>{u("")},1e3)},[]),c=s.useCallback((u,x="polite")=>{!u||typeof u!="string"||(x==="assertive"?(i(u),l(i)):(a(u),l(a)))},[l]),g=s.useCallback(u=>{c(u,"polite")},[c]),n=s.useCallback(u=>{c(u,"assertive")},[c]),h={announce:c,announcePolite:g,announceAssertive:n};return e.jsxs(Q.Provider,{value:h,children:[e.jsx("div",{"aria-live":"polite","aria-atomic":"true",style:z,children:r}),e.jsx("div",{"aria-live":"assertive","aria-atomic":"true",style:z,children:d}),t]})},U=()=>{const t=s.useContext(Q);return t||(console.warn("useAnnouncer must be used within an AnnouncerProvider"),{announce:()=>{},announcePolite:()=>{},announceAssertive:()=>{}})};function de({trigger:t,children:r,className:a="right-0 mt-2 w-48"}){const[d,i]=I.useState(!1),o=c=>{c.preventDefault(),c.stopPropagation(),i(g=>!g)};I.useEffect(()=>{const c=()=>i(!1);return window.addEventListener("click",c),()=>window.removeEventListener("click",c)},[]);const l=c=>{c.stopPropagation()};return e.jsxs("div",{className:"relative inline-block text-left profile-menu-container",onClick:o,children:[t,d&&e.jsx("div",{className:`absolute ${a} rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-50 focus:outline-none z-50`,role:"menu","aria-orientation":"vertical",tabIndex:"-1",onClick:l,children:e.jsx("div",{className:"py-1",role:"none",children:r})})]})}function ce(){return e.jsx("a",{href:"#main-content",className:"sr-only focus:not(.sr-only)",children:e.jsx("span",{className:"fixed top-0 left-0 m-4 p-3 bg-indigo-600 text-white rounded z-50",children:"Skip to content"})})}function _({user:t,children:r}){var i;const[a,d]=I.useState(!1);return I.useEffect(()=>{(async()=>{let l=!1;if(t)try{const c=await fetch("/api/user/dark-mode",{credentials:"include"});c.ok&&(l=(await c.json()).dark_mode)}catch{console.log("Could not fetch dark mode preference from server")}if(!t){const c=localStorage.getItem("darkMode");l=c==="true"||c===null&&window.matchMedia("(prefers-color-scheme: dark)").matches}d(l)})()},[t]),I.useEffect(()=>{a?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode",a?"true":"false"),t&&fetch("/api/user/dark-mode",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({dark_mode:a})}).catch(o=>{console.log("Could not save dark mode preference to server:",o)})},[a,t]),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300",children:[e.jsx(ce,{}),e.jsxs("header",{className:"bg-white dark:bg-gray-800 shadow-sm p-4 flex justify-between items-center transition-colors duration-300",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("svg",{className:"w-6 h-6 text-indigo-600",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:e.jsx("path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"})}),e.jsx("h1",{className:"text-xl font-bold",children:"RealHonest – get real, be real"})]}),e.jsxs("nav",{className:"space-x-4 hidden md:flex items-center",children:[e.jsx("a",{href:"/",className:"hover:text-indigo-600 transition",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"hover:text-indigo-600 transition",children:"Dashboard"}),e.jsx("a",{href:"/mentors",className:"hover:text-indigo-600 transition",children:"Mentors"}),t&&e.jsxs(de,{trigger:e.jsxs("button",{type:"button","aria-label":"User menu",className:"focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full ml-4",children:[t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?e.jsx("img",{src:t.avatar_url,alt:"User avatar",className:"w-8 h-8 rounded-full border-2 border-indigo-500 hover:border-indigo-700 transition object-cover",onError:o=>{o.target.style.display="none",o.target.nextSibling.style.display="flex"}}):null,e.jsx("div",{className:"w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-semibold border-2 border-indigo-500 hover:border-indigo-700 transition",style:{display:t.avatar_url&&t.avatar_url!=="/static/avatars/default.png"&&t.avatar_url.includes("/static/avatars/")?"none":"flex"},children:((i=t.username)==null?void 0:i.charAt(0).toUpperCase())||"?"})]}),className:"right-0 mt-2 w-48 z-50",children:[e.jsx(T,{to:"/profile",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"🧾 Settings"}),e.jsx(T,{to:"/archived-chats",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📁 Archived Chats"}),e.jsx(T,{to:"/download",className:"block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700",children:"📲 Download App"}),e.jsx("button",{type:"button",className:"w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-900",onClick:async()=>{if(window.confirm("Are you sure you want to delete your account?"))try{(await fetch("/api/user",{method:"DELETE",credentials:"include"})).ok?window.location.href="/logout":alert("Failed to delete account")}catch{alert("Error deleting account")}},children:"❌ Delete Account"})]}),e.jsx("button",{onClick:()=>d(!a),"aria-label":a?"Switch to light mode":"Switch to dark mode",className:"ml-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 rounded-md px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:a?"🌞 Light Mode":"🌙 Dark Mode"}),e.jsx("a",{href:"/login",className:"ml-4 hover:text-red-600 transition",children:"🚪 Login"}),e.jsx("a",{href:"/logout",className:"ml-4 hover:text-red-600 transition",children:"🚪 Logout"})]})]}),e.jsxs("main",{className:"flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6",children:[t&&e.jsx("aside",{className:"md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sticky top-6 transition-colors duration-300",children:e.jsxs("nav",{className:"space-y-2",children:[e.jsx("a",{href:"/",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🏠 Home"}),e.jsx("a",{href:"/dashboard",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📊 Dashboard"}),e.jsx("a",{href:"/archives",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📁 Archives"}),e.jsx("a",{href:"/activity",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"📈 Activity"}),e.jsx("a",{href:"/announcer-demo",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"🔊 Accessibility Demo"}),e.jsx("a",{href:"/profile",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"⚙️ Profile"}),e.jsx("a",{href:"/chat",className:"block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors",children:"💬 Chat"}),e.jsx("a",{href:"/logout",className:"block py-2 px-3 hover:bg-red-100 dark:hover:bg-red-900 rounded text-red-600 dark:text-red-400 transition-colors",children:"🚪 Logout"})]})}),e.jsx("section",{id:"main-content",className:"flex-grow",children:r})]}),e.jsxs("footer",{className:"py-6 px-4 text-center text-gray-500 text-sm bg-white border-t mt-auto",children:["© ",new Date().getFullYear()," RealHonest – get real, be real. All rights reserved."]})]})}function me({postId:t,maxComments:r=2}){const[a,d]=s.useState([]),[i,o]=s.useState(!0),[l,c]=s.useState(0);s.useEffect(()=>{g()},[t]);const g=async()=>{try{const n=await fetch(`/api/comments/${t}`,{credentials:"include"});if(n.ok){const h=await n.json();d(h.slice(0,r)),c(h.length)}}catch(n){console.error("Error fetching comments:",n)}finally{o(!1)}};return i?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500",children:"Loading comments..."})}):a.length===0?e.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:e.jsx("p",{className:"text-sm text-gray-500 italic",children:"No comments yet. Be the first to comment!"})}):e.jsxs("div",{className:"mt-4 space-y-3",children:[a.map((n,h)=>{var u;return e.jsx("div",{className:"bg-gray-50 p-3 rounded-lg",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"w-6 h-6 bg-indigo-400 rounded-full flex items-center justify-center text-white text-xs font-semibold",children:((u=n.author)==null?void 0:u.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:n.author||"Anonymous"}),e.jsx("span",{className:"text-xs text-gray-500",children:new Date(n.created_at).toLocaleDateString()})]}),e.jsx("p",{className:"text-sm text-gray-700 mt-1 break-words",children:n.content})]})]})},n.id||h)}),l>r&&e.jsx("div",{className:"text-center",children:e.jsxs("a",{href:`/posts/${t}#comments`,className:"text-sm text-indigo-600 hover:text-indigo-800 font-medium",children:["View all ",l," comments →"]})})]})}const ue=t=>`${window.location.origin}/posts/${t}`,he=t=>{const r=t.content.replace(/<[^>]*>/g,"").slice(0,100);return`${t.title}

${r}${r.length>=100?"...":""}`},xe=async t=>{try{return await navigator.clipboard.writeText(t),!0}catch(r){return console.error("Failed to copy to clipboard:",r),!1}},ge=(t,r=120)=>`https://api.qrserver.com/v1/create-qr-code/?size=${r}x${r}&data=${encodeURIComponent(t)}`,A={whatsapp:(t,r,a)=>`https://wa.me/?text=${encodeURIComponent(`${t}

${r}

${a}`)}`,email:(t,r,a)=>`mailto:?subject=${encodeURIComponent(t)}&body=${encodeURIComponent(`${r}

Read more: ${a}`)}`,twitter:(t,r,a)=>`https://twitter.com/intent/tweet?text=${encodeURIComponent(`${t}

${r}`)}&url=${encodeURIComponent(a)}`,facebook:(t,r,a)=>`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(a)}`,linkedin:(t,r,a)=>`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(a)}`,telegram:(t,r,a)=>`https://t.me/share/url?url=${encodeURIComponent(a)}&text=${encodeURIComponent(`${t}

${r}`)}`,reddit:(t,r,a)=>`https://reddit.com/submit?url=${encodeURIComponent(a)}&title=${encodeURIComponent(t)}`,pinterest:(t,r,a)=>`https://pinterest.com/pin/create/button/?url=${encodeURIComponent(a)}&description=${encodeURIComponent(`${t}

${r}`)}`};function W({isOpen:t,onClose:r,post:a}){const[d,i]=s.useState(!1);if(!t||!a)return null;const o=ue(a.id),l=a.title,c=he(a),g=async()=>{await xe(o)?(i(!0),setTimeout(()=>i(!1),2e3)):alert("Failed to copy link")},n=[{name:"WhatsApp",icon:"💬",color:"bg-green-500 hover:bg-green-600",url:A.whatsapp(l,c,o)},{name:"Email",icon:"📧",color:"bg-blue-500 hover:bg-blue-600",url:A.email(l,c,o)},{name:"Twitter",icon:"🐦",color:"bg-sky-500 hover:bg-sky-600",url:A.twitter(l,c,o)},{name:"Facebook",icon:"📘",color:"bg-blue-600 hover:bg-blue-700",url:A.facebook(l,c,o)},{name:"LinkedIn",icon:"💼",color:"bg-blue-700 hover:bg-blue-800",url:A.linkedin(l,c,o)},{name:"Telegram",icon:"✈️",color:"bg-blue-400 hover:bg-blue-500",url:A.telegram(l,c,o)},{name:"Reddit",icon:"🤖",color:"bg-orange-500 hover:bg-orange-600",url:A.reddit(l,c,o)},{name:"Pinterest",icon:"📌",color:"bg-red-500 hover:bg-red-600",url:A.pinterest(l,c,o)}],h=u=>{window.open(u,"_blank","width=600,height=400")};return e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Share Post"}),e.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 text-xl",children:"✕"})]}),e.jsxs("div",{className:"p-4 border-b bg-gray-50",children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:l}),e.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:c})]}),e.jsxs("div",{className:"p-4 border-b",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Copy Link"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"text",value:o,readOnly:!0,className:"flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"}),e.jsx("button",{onClick:g,className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${d?"bg-green-100 text-green-700":"bg-indigo-600 text-white hover:bg-indigo-700"}`,children:d?"✓ Copied!":"Copy"})]})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"Share via"}),e.jsx("div",{className:"grid grid-cols-2 gap-3",children:n.map(u=>e.jsxs("button",{onClick:()=>h(u.url),className:`flex items-center space-x-3 p-3 rounded-lg text-white transition-colors ${u.color}`,children:[e.jsx("span",{className:"text-lg",children:u.icon}),e.jsx("span",{className:"font-medium",children:u.name})]},u.name))})]}),e.jsxs("div",{className:"p-4 border-t bg-gray-50",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"QR Code"}),e.jsx("div",{className:"flex justify-center",children:e.jsx("img",{src:ge(o,120),alt:"QR Code",className:"w-24 h-24 border rounded"})}),e.jsx("p",{className:"text-xs text-gray-500 text-center mt-2",children:"Scan to open post"})]})]})})}function pe({post:t,user:r}){var y;const[a,d]=s.useState(!1),[i,o]=s.useState({comments_count:0,user_saved:!1}),[l,c]=s.useState(!1),[g,n]=s.useState(!1),[h,u]=s.useState(""),[x,b]=s.useState(!1),[m,v]=s.useState(!1),j=R();s.useEffect(()=>{f()},[t.id]);const f=async()=>{try{const w=await fetch(`/api/posts/${t.id}/stats`,{credentials:"include"});if(w.ok){const N=await w.json();o(N)}}catch(w){console.error("Error fetching post stats:",w)}},p=async()=>{if(!r){alert("Please log in to save posts");return}c(!0);try{const w=await fetch(`/api/posts/${t.id}/save`,{method:"POST",credentials:"include"});if(w.ok){const N=await w.json();o(E=>({...E,user_saved:N.saved})),alert(N.saved?"Post saved!":"Post unsaved!")}}catch(w){console.error("Error toggling save:",w),alert("Failed to toggle save")}finally{c(!1)}},k=()=>{v(!0)},P=()=>{if(!r){alert("Please log in to comment");return}n(!g)},D=async w=>{if(w.preventDefault(),!!h.trim()){b(!0);try{const N=await fetch(`/api/comments/${t.id}`,{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({content:h})});if(N.ok)u(""),n(!1),f(),window.location.reload();else{const E=await N.json().catch(()=>({detail:"Failed to post comment"}));alert(`Failed to post comment: ${E.detail||"Unknown error"}`)}}catch(N){console.error("Error posting comment:",N),alert("Failed to post comment")}finally{b(!1)}}},$=t.content.replace(/<[^>]*>/g,""),S=$.length>200,C=a?t.content:$.length>200?t.content.slice(0,200)+"...":t.content;return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold",children:((y=t.author_username)==null?void 0:y.charAt(0).toUpperCase())||"U"}),e.jsxs("div",{className:"ml-3",children:[e.jsx("p",{className:"font-semibold text-gray-900 dark:text-gray-100",children:t.author_username||"Unknown"}),e.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(t.created_at).toLocaleDateString()})]})]}),e.jsx("h4",{className:"text-xl font-bold mb-3 text-gray-900",children:t.title}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full max-w-md mx-auto rounded-lg mb-4 cursor-pointer",onClick:()=>j(`/posts/${t.id}`)}),e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"text-gray-700 leading-relaxed prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:C}}),S&&e.jsx("button",{onClick:()=>d(!a),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2",children:a?"Show Less":"Read More"})]}),e.jsxs("div",{className:"flex items-center justify-between pt-4 border-t border-gray-100",children:[e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsxs("button",{onClick:P,className:"flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"💬"}),e.jsx("span",{className:"text-sm font-medium",children:i.comments_count})]}),e.jsxs("button",{onClick:k,className:"flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors",children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("button",{onClick:p,disabled:l,className:`transition-colors ${i.user_saved?"text-yellow-500 hover:text-yellow-600":"text-gray-500 hover:text-yellow-500"} ${l?"opacity-50 cursor-not-allowed":""}`,children:e.jsx("span",{className:"text-lg",children:i.user_saved?"🔖":"📑"})}),e.jsx("button",{onClick:()=>j(`/posts/${t.id}`),className:"text-indigo-600 hover:text-indigo-800 text-sm font-medium",children:"View Full Post →"})]})]}),g&&r&&e.jsx("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:e.jsxs("form",{onSubmit:D,children:[e.jsx("textarea",{value:h,onChange:w=>u(w.target.value),placeholder:"Write a comment...",className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent",rows:"3",disabled:x}),e.jsxs("div",{className:"flex justify-end space-x-2 mt-2",children:[e.jsx("button",{type:"button",onClick:()=>n(!1),className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800",disabled:x,children:"Cancel"}),e.jsx("button",{type:"submit",disabled:x||!h.trim(),className:"px-4 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Posting...":"Post Comment"})]})]})}),e.jsx(me,{postId:t.id,maxComments:2}),e.jsx(W,{isOpen:m,onClose:()=>v(!1),post:t})]})}function be({user:t}){const[r,a]=s.useState([]),[d,i]=s.useState("Loading fact..."),o=()=>{fetch("/api/posts").then(l=>l.json()).then(a).catch(l=>console.error("Error fetching posts:",l))};return s.useEffect(()=>{o(),fetch("https://numbersapi.com/random/trivia").then(l=>l.text()).then(i).catch(l=>{console.error("Failed to load number fact",l),i("There's always something new to learn.")})},[]),s.useEffect(()=>{const l=g=>{g.key==="postsUpdated"&&(o(),localStorage.removeItem("postsUpdated"))};window.addEventListener("storage",l);const c=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(o(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",c),()=>{window.removeEventListener("storage",l),document.removeEventListener("visibilitychange",c)}},[]),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"py-16 px-4 text-center bg-gradient-to-b from-indigo-50 to-white",children:[e.jsx("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600",children:"Real Honest Conversations for Visionary Minds"}),e.jsx("p",{className:"text-lg max-w-2xl mx-auto mb-8 text-gray-800",children:"A platform for scholars, entrepreneurs, and community leaders to share authentic insights, mentor others, and build a better world without the noise of likes or dislikes."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:t?e.jsx("a",{href:"/dashboard",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition",children:"Go to Dashboard"}):e.jsxs(e.Fragment,{children:[e.jsx("a",{href:"/login",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105",children:"Join Be Real, Get Real"}),e.jsx("a",{href:"#features",className:"px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition",children:"Learn More"})]})})]}),e.jsx("section",{id:"features",className:"py-16 px-4 bg-gray-100",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("h2",{className:"text-2xl font-bold text-center mb-10",children:"Core Features"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"WebRTC Chat"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Secure peer-to-peer video/audio communication between mentors and mentees."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Offline Capabilities"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Built as a Progressive Web App (PWA) — use it anywhere, even without internet access."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"No Popularity Metrics"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Focus on authentic content and meaningful discussions — no likes, no fake stories, no popularity contests."})]}),e.jsxs("div",{className:"bg-white dark:bg-gray-700 p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h3",{className:"font-semibold text-lg mb-2 text-gray-900 dark:text-gray-100",children:"Community Building"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Create and join topic-based communities focused on research, education, or social impact."})]})]})]})}),e.jsxs("section",{className:"py-16 px-4 text-center",children:[e.jsx("h2",{className:"text-2xl font-bold mb-8 text-gray-900 dark:text-gray-100",children:"Recent Community Insights"}),r.length>0?e.jsx("div",{className:"max-w-4xl mx-auto space-y-6",children:r.map(l=>e.jsx(pe,{post:l,user:t},l.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function fe({onPostCreated:t}){const[r,a]=s.useState(""),[d,i]=s.useState(""),[o,l]=s.useState(null),[c,g]=s.useState(null),[n,h]=s.useState(!1),[u,x]=s.useState(""),[b,m]=s.useState(""),v=s.useRef(null),j=()=>{const S=document.createElement("input");S.setAttribute("type","file"),S.setAttribute("accept","image/*"),S.click(),S.onchange=async()=>{const C=S.files[0];if(C){const y=new FormData;y.append("image",C);try{const w=await fetch("/api/upload-image",{method:"POST",body:y});if(w.ok){const E=(await w.json()).url,L=v.current.getEditor(),M=L.getSelection();L.insertEmbed(M.index,"image",E)}else{const N=new FileReader;N.onload=E=>{const L=v.current.getEditor(),M=L.getSelection();L.insertEmbed(M.index,"image",E.target.result)},N.readAsDataURL(C)}}catch(w){console.error("Image upload failed:",w);const N=new FileReader;N.onload=E=>{const L=v.current.getEditor(),M=L.getSelection();L.insertEmbed(M.index,"image",E.target.result)},N.readAsDataURL(C)}}}},f=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:j}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),p=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"],k=S=>{const C=S.replace(/<[^>]*>/g,"").trim();return C?C.split(/\s+/).length:0},P=S=>{const C=k(S);return Math.ceil(C/200)},D=S=>{const C=S.target.files[0];if(C){l(C);const y=new FileReader;y.onloadend=()=>g(y.result),y.readAsDataURL(C)}},$=async S=>{if(S.preventDefault(),h(!0),x(""),m(""),!d.replace(/<[^>]*>/g,"").trim()){x("Please enter some content for your post"),h(!1);return}const y=new FormData;y.append("title",r),y.append("content",d),o&&y.append("image",o);try{const w=await fetch("/api/posts",{method:"POST",body:y,credentials:"include"});if(!w.ok){const N=await w.json().catch(()=>({detail:"Failed to create post"}));throw new Error(N.detail||"Something went wrong")}await w.json(),m("✅ Post published successfully!"),a(""),i(""),l(null),g(null),localStorage.setItem("postsUpdated",Date.now().toString()),setTimeout(t?()=>{t()}:()=>{window.location.href="/dashboard"},1e3)}catch(w){x(w.message||"❌ Error creating post")}finally{h(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded shadow-md mb-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"✍️ Create New Post"}),e.jsx("style",{jsx:!0,children:`
        .ql-editor {
          min-height: 250px;
          font-size: 14px;
          line-height: 1.6;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }
        .ql-toolbar {
          border-top: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-bottom: none;
          border-radius: 0.375rem 0.375rem 0 0;
          background: #f9fafb;
        }
        .ql-container {
          border-bottom: 1px solid #d1d5db;
          border-left: 1px solid #d1d5db;
          border-right: 1px solid #d1d5db;
          border-top: none;
          border-radius: 0 0 0.375rem 0.375rem;
        }
        .ql-editor.ql-blank::before {
          color: #9ca3af;
          font-style: italic;
        }
        .ql-toolbar .ql-formats {
          margin-right: 8px;
        }
        .ql-toolbar button {
          padding: 4px;
          margin: 1px;
        }
        .ql-toolbar button:hover {
          background: #e5e7eb;
          border-radius: 3px;
        }
        .ql-toolbar .ql-active {
          background: #dbeafe;
          color: #1d4ed8;
          border-radius: 3px;
        }
        .ql-editor img {
          max-width: 100%;
          height: auto;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .ql-editor blockquote {
          border-left: 4px solid #e5e7eb;
          padding-left: 16px;
          margin: 16px 0;
          font-style: italic;
          color: #6b7280;
        }
        .ql-editor pre.ql-syntax {
          background: #f3f4f6;
          border: 1px solid #e5e7eb;
          border-radius: 4px;
          padding: 12px;
          overflow-x: auto;
        }
        .ql-editor .ql-video {
          width: 100%;
          height: 315px;
        }
        .ql-snow .ql-tooltip {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .ql-snow .ql-tooltip input[type=text] {
          border: 1px solid #d1d5db;
          border-radius: 4px;
          padding: 4px 8px;
        }
        .ql-snow .ql-picker-options {
          background: white;
          border: 1px solid #d1d5db;
          border-radius: 4px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
      `}),u&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:u}),b&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:b}),e.jsxs("form",{onSubmit:$,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:r,onChange:S=>a(S.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",d.replace(/<[^>]*>/g,"").length," characters • ",k(d)," words • ~",P(d)," min read)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(Y,{ref:v,theme:"snow",value:d,onChange:i,modules:f,formats:p,placeholder:"Write your post content here... Use the enhanced toolbar for rich formatting, images, and more!",readOnly:n,style:{minHeight:"300px",backgroundColor:n?"#f3f4f6":"white"}})}),d&&e.jsxs("div",{className:"mt-2 space-y-1",children:[e.jsxs("div",{className:"text-xs text-gray-500",children:["✨ ",e.jsx("strong",{children:"Enhanced Features:"})," Images, videos, formulas, tables, colors, fonts, and more!"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["📸 ",e.jsx("strong",{children:"Images:"})," Click the image icon to upload • 🎨 ",e.jsx("strong",{children:"Colors:"})," Highlight text and use color tools • 📐 ",e.jsx("strong",{children:"Math:"})," Use formula button for equations"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:D,disabled:n,className:"w-full px-2 py-1 border border-gray-300 rounded"})]}),c&&e.jsx("img",{src:c,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:n,className:`w-full py-2 px-4 rounded text-white font-medium transition ${n?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:n?"Publishing...":"Publish Post"})]})]})}function ye({post:t,className:r="",children:a}){const[d,i]=s.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>i(!0),className:r,children:a||e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-lg",children:"📤"}),e.jsx("span",{className:"text-sm font-medium",children:"Share"})]})}),e.jsx(W,{isOpen:d,onClose:()=>i(!1),post:t})]})}function je({user:t}){const[r,a]=s.useState(null),[d,i]=s.useState([]),[o,l]=s.useState("Loading fact..."),[c,g]=s.useState(!1),n=R(),{announcePolite:h,announceAssertive:u}=U();s.useEffect(()=>{t!=null&&t.github_username&&fetch(`https://api.github.com/users/${t.github_username}`).then(m=>m.json()).then(m=>a({followers:m.followers,public_repos:m.public_repos,profile_url:m.html_url})).catch(m=>console.error("Failed to fetch GitHub data",m))},[t]);const x=()=>{fetch("/api/posts").then(m=>m.json()).then(m=>{i(m),h(`Dashboard updated with ${m.length} posts`)}).catch(m=>{console.error(m),u("Failed to load posts")})};s.useEffect(()=>{x()},[]),s.useEffect(()=>{const m=j=>{j.key==="postsUpdated"&&x()};window.addEventListener("storage",m);const v=()=>{!document.hidden&&localStorage.getItem("postsUpdated")&&(x(),localStorage.removeItem("postsUpdated"))};return document.addEventListener("visibilitychange",v),()=>{window.removeEventListener("storage",m),document.removeEventListener("visibilitychange",v)}},[]),s.useEffect(()=>{fetch("https://numbersapi.com/random/trivia").then(m=>m.text()).then(l).catch(m=>{console.error("Failed to load number fact",m),l("There's always something new to learn.")})},[]);const b=async m=>{if(window.confirm("Are you sure you want to delete this post?"))try{const j=await fetch(`/api/posts/${m}`,{method:"DELETE",credentials:"include"});if(!j.ok){const f=await j.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(f.detail||"Something went wrong")}h("Post deleted successfully"),x()}catch(j){u(`Failed to delete post: ${j.message}`),alert(j.message)}};return!t||t.role!=="author"?e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Only authors can access dashboard"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("section",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8",children:[e.jsxs("h1",{className:"text-2xl font-extrabold",children:["Welcome back, ",t.full_name||t.username]}),e.jsx("p",{className:"mt-2 opacity-90",children:"Share insights, connect, and build honest conversations."})]}),!t.mentor_profile&&e.jsxs("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-8 transition-colors duration-300",children:[e.jsx("h2",{className:"text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Want to Become a Mentor?"}),e.jsx("p",{className:"mb-4 text-gray-700 dark:text-gray-300",children:"Help others grow by offering guidance in your field."}),e.jsx("a",{href:"/register-mentor",className:"px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105","aria-label":"Register as a mentor to help others grow",children:"🎓 Register as Mentor"})]}),(t==null?void 0:t.github_username)&&r&&e.jsxs("div",{className:"mt-6 bg-white dark:bg-gray-800 p-4 rounded shadow-sm transition-colors duration-300",children:[e.jsx("h2",{className:"font-semibold mb-2 text-gray-900 dark:text-gray-100",children:"GitHub Profile"}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Followers: ",r.followers]}),e.jsxs("p",{className:"text-gray-700 dark:text-gray-300",children:["Repositories: ",r.public_repos]}),e.jsx("p",{children:e.jsx("a",{href:r.profile_url,target:"_blank",rel:"noopener noreferrer",className:"text-indigo-600 dark:text-indigo-400 underline",children:"View on GitHub"})})]}),e.jsx("button",{onClick:()=>g(!c),type:"button",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition mb-6","aria-label":"Create a new post",children:"✏️ New Post"}),c&&e.jsx(fe,{onPostCreated:()=>{x(),g(!1),localStorage.setItem("postsUpdated",Date.now().toString())}}),e.jsxs("section",{className:"bg-indigo-50 p-4 rounded mb-6",children:[e.jsx("h3",{className:"font-semibold",children:"Today’s Number Fact"}),e.jsx("p",{children:o})]}),e.jsxs("section",{className:"space-y-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100",children:"Recent Community Posts"}),d.length>0?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(m=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:m.title}),e.jsx("div",{className:"text-gray-700 mb-4 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:m.content.length>150?m.content.slice(0,150)+"...":m.content}}),m.image_url&&e.jsx("img",{src:m.image_url,alt:m.title,className:"w-full h-48 object-cover rounded mb-4",onError:v=>{v.target.style.display="none"}}),e.jsx("small",{className:"text-sm text-gray-500",children:new Date(m.created_at).toLocaleString()}),e.jsxs("div",{className:"mt-4 flex gap-2",children:[e.jsx("a",{href:`/posts/${m.id}`,className:"text-indigo-600 hover:underline",children:"View"}),e.jsx(ye,{post:m,className:"text-green-600 hover:underline",children:"📤 Share"}),e.jsx("button",{id:`edit-post-${m.id}`,onClick:()=>n(`/posts/${m.id}/edit`),"aria-label":`Edit ${m.title}`,className:"text-blue-600 hover:underline",children:"🖋️ Edit"}),e.jsx("button",{id:`delete-post-${m.id}`,onClick:()=>b(m.id),"aria-label":`Delete ${m.title}`,className:"text-red-600 hover:underline",children:"🗑️ Delete"})]})]},m.id))}):e.jsx("p",{className:"italic text-gray-500",children:"No posts yet. Be the first to share something meaningful!"})]})]})}function ve({onLogin:t}){const[r,a]=s.useState(""),[d,i]=s.useState(""),[o,l]=s.useState(""),c=async g=>{g.preventDefault();const n=new FormData;n.append("username",r),n.append("password",d);try{const h=await fetch("/login",{method:"POST",body:n,credentials:"include"});if(!h.ok){const x=await h.json();throw new Error(x.detail||"Invalid credentials")}const u=await h.json();t(u),window.location.href="/dashboard"}catch(h){l(h.message||"Failed to log in")}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Login to RealHonest"}),o&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",o]}),e.jsxs("form",{onSubmit:c,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",value:r,onChange:g=>a(g.target.value),required:!0,autoComplete:"username",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",value:d,onChange:g=>i(g.target.value),required:!0,autoComplete:"current-password",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500"})]}),e.jsx("button",{type:"submit",className:"w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded",children:"Login"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Don't have an account?"," ",e.jsx("a",{href:"/signup",className:"text-indigo-600 hover:underline",children:"Sign up"})]})]})}function we({onLogin:t}){const[r,a]=s.useState(""),[d,i]=s.useState(""),[o,l]=s.useState(""),[c,g]=s.useState(""),[n,h]=s.useState(""),[u,x]=s.useState(!1),b=async m=>{m.preventDefault(),x(!0),g(""),h("");const v=new FormData;v.append("username",r),v.append("email",d),v.append("password",o);try{const j=await fetch("/signup",{method:"POST",body:v,credentials:"include",headers:{"X-Requested-With":"XMLHttpRequest"}});if(j.redirected){window.location.href=j.url;return}if(!j.ok){const p=await j.json().catch(()=>({detail:"Signup failed"}));throw new Error(p.detail||"Something went wrong")}const f=await j.json();f.redirect?window.location.href=f.redirect:window.location.href="/profile"}catch(j){g(j.message||"Failed to register")}finally{x(!1)}};return e.jsxs("main",{className:"max-w-md w-full mx-auto p-6 bg-white rounded shadow-md",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Create Your Account"}),c&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded mb-4",children:["❌ ",c]}),n&&e.jsxs("div",{className:"bg-green-100 text-green-700 p-3 rounded mb-4",children:["✅ ",n]}),e.jsxs("form",{onSubmit:b,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"Username"}),e.jsx("input",{id:"username",type:"text",autoComplete:"username",value:r,onChange:m=>a(m.target.value),required:!0,disabled:u,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),e.jsx("input",{id:"email",type:"email",autoComplete:"email",value:d,onChange:m=>i(m.target.value),required:!0,disabled:u,placeholder:"<EMAIL>",className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),e.jsx("input",{id:"password",type:"password",autoComplete:"new-password",value:o,onChange:m=>l(m.target.value),required:!0,disabled:u,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsx("button",{type:"submit",disabled:u,className:`w-full bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded transition ${u?"opacity-70 cursor-not-allowed":""}`,children:u?"Creating account...":"Sign Up"})]}),e.jsxs("p",{className:"mt-4 text-center text-sm text-gray-600",children:["Already have an account?"," ",e.jsx("a",{href:"/login",className:"text-indigo-600 hover:underline",children:"Login"})]})]})}function Ne({user:t}){const[r,a]=s.useState(""),[d,i]=s.useState("Research"),[o,l]=s.useState(""),[c,g]=s.useState(""),[n,h]=s.useState(""),[u,x]=s.useState(0),[b,m]=s.useState(""),[v,j]=s.useState(!0),f=async p=>{p.preventDefault();const k={full_name:r,category:d,expertise:o,bio:c,qualifications:n,experience_years:parseInt(u),hourly_rate:b,available:v};try{if(!(await fetch("/register-mentor",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(k),credentials:"include"})).ok)throw new Error("Registration failed");alert("Successfully registered as mentor!"),window.location.href="/profile"}catch(P){console.error("Error registering:",P),alert("Failed to register mentor.")}};return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Become a Mentor"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Help others grow by offering guidance in your field."}),e.jsxs("form",{onSubmit:f,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:r,onChange:p=>a(p.target.value),required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"Mentor Category"}),e.jsx("select",{id:"category",value:d,onChange:p=>i(p.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none",required:!0,children:["Research","Entrepreneurship","Education","Social Impact"].map(p=>e.jsx("option",{value:p,children:p},p))})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"expertise",className:"block text-sm font-medium text-gray-700 mb-2",children:"Expertise"}),e.jsx("input",{id:"expertise",type:"text",value:o,onChange:p=>l(p.target.value),required:!0,placeholder:"e.g., AI Ethics, Startup Strategy",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-2",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:c,onChange:p=>g(p.target.value),required:!0,placeholder:"What do you specialize in?",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"qualifications",className:"block text-sm font-medium text-gray-700 mb-2",children:"Qualifications"}),e.jsx("textarea",{id:"qualifications",rows:"4",value:n,onChange:p=>h(p.target.value),required:!0,placeholder:"e.g., PhD in Computer Science, 10+ years experience",className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:indigo-500 outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"experience_years",className:"block text-sm font-medium text-gray-700 mb-2",children:"Years of Experience"}),e.jsx("input",{id:"experience_years",type:"number",min:"0",max:"50",value:u,onChange:p=>x(parseInt(p.target.value)),className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"hourly_rate",className:"block text-sm font-medium text-gray-700 mb-2",children:"Hourly Rate"}),e.jsx("input",{id:"hourly_rate",type:"text",value:b,onChange:p=>m(p.target.value),placeholder:"e.g., $50/hour",className:"w-full px-4 py-2 border border-gray-300 rounded-md",required:!0})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",checked:v,onChange:()=>j(!v),className:"mr-2"}),e.jsx("label",{htmlFor:"available",className:"text-sm text-gray-700",children:"I am available for mentoring sessions"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{type:"submit",className:"px-5 py-2 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-md shadow transition",children:"Register as Mentor"})})]})]})})}function ke({user:t}){const{mentorId:r}=q(),[a,d]=s.useState(null),[i,o]=s.useState(!0);return s.useEffect(()=>{fetch(`/api/mentors/${r}`).then(l=>l.json()).then(d).finally(()=>o(!1))},[r]),i?e.jsx("p",{children:"Loading mentor profile..."}):a?e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:a.full_name}),e.jsxs("p",{className:"text-indigo-600 font-medium mt-1",children:["Category: ",a.category]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Expertise:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:a.expertise})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Bio:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:a.bio||"No bio provided."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Qualifications:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:a.qualifications||"Not specified."})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Experience:"}),e.jsxs("p",{className:"mt-2 text-gray-700",children:[a.experience_years," years"]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("strong",{children:"Hourly Rate:"}),e.jsx("p",{className:"mt-2 text-gray-700",children:a.hourly_rate})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:`/dm/${a.user.username}`,className:"px-4 py-2 bg-indigo-600 text-white rounded-md transition",children:"💬 Message Mentor"}),(t==null?void 0:t.role)==="author"&&e.jsx("a",{href:`/edit-mentor/${a.id}`,className:"px-4 py-2 bg-green-600 text-white rounded-md transition",children:"✏️ Edit Profile"})]})]})}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Mentor not found"}),e.jsx("a",{href:"/mentors",className:"text-indigo-600 underline",children:"Browse All Mentors"})]})}function Se({user:t}){const[r,a]=s.useState([]),[d,i]=s.useState(""),o=s.useRef(null),l=s.useRef(null);s.useEffect(()=>{var h;if(!t)return;const n=(h=document.cookie.split("; ").find(u=>u.startsWith("access_token=")))==null?void 0:h.split("=")[1];l.current=new WebSocket("ws://localhost:8000/ws/chat"),l.current.onopen=()=>{l.current.send(JSON.stringify({token:n}))},l.current.onmessage=u=>{try{const x=JSON.parse(u.data);a(b=>[...b,x])}catch{console.error("Failed to parse message:",u.data)}}},[t]);const c=n=>{var u;if(n.preventDefault(),!d.trim())return;if(g(d)){alert("Your message contains restricted content.");return}const h={sender:(t==null?void 0:t.username)||"Anonymous",text:d,timestamp:new Date().toISOString()};((u=l.current)==null?void 0:u.readyState)===WebSocket.OPEN?l.current.send(JSON.stringify(h)):console.warn("WebSocket not connected"),a(x=>[...x,h]),i("")},g=n=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(u=>n.toLowerCase().includes(u));return s.useEffect(()=>{o.current&&(o.current.scrollTop=o.current.scrollHeight)},[r]),t?e.jsxs("main",{className:"max-w-4xl mx-auto p-6",children:[e.jsxs("section",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Video Chat with Mentor"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"localVideo",autoPlay:!0,muted:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"You"})]}),e.jsxs("div",{className:"bg-black rounded overflow-hidden aspect-video relative",children:[e.jsx("video",{id:"remoteVideo",autoPlay:!0,playsInline:!0,className:"w-full h-full object-cover"}),e.jsx("div",{className:"absolute bottom-2 left-2 bg-black bg-opacity-50 text-white px-2 py-1 text-sm rounded",children:"Remote"})]})]}),e.jsxs("div",{className:"mt-4 flex justify-center space-x-4",children:[e.jsx("button",{id:"startCallBtn",type:"button","aria-label":"Start video call with mentor",className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md shadow-md transition",children:"Start Call"}),e.jsx("button",{id:"endCallBtn",type:"button","aria-label":"End video call",className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md shadow-md transition",children:"End Call"})]})]}),e.jsxs("section",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsx("h3",{className:"font-semibold mb-4",children:"Chat Messages"}),e.jsx("div",{id:"messageList",ref:o,className:"space-y-4 max-h-96 overflow-y-auto p-2 border border-gray-200 rounded",children:r.length>0?r.map((n,h)=>e.jsx("div",{className:`py-2 ${n.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded-lg ${n.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:n.sender}),": ",n.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(n.timestamp).toLocaleTimeString()})]})},h)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{id:"chatForm",onSubmit:c,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{id:"messageInput",type:"text",value:d,onChange:n=>i(n.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition",children:"Send"})]})]})]}):e.jsxs("div",{className:"text-center py-10",children:[e.jsx("p",{className:"text-red-500 mb-4",children:"Please log in to use the chat"}),e.jsx("a",{href:"/login",className:"text-indigo-600 underline",children:"Go to Login"})]})}function Ce({user:t}){const[r,a]=s.useState([]),[d,i]=s.useState(""),o=s.useRef(null);s.useEffect(()=>{const g=new WebSocket(`ws://localhost:8000/ws/dm/${t.username}?token=Bearer%20${localStorage.getItem("access_token")}`);g.onmessage=n=>{try{const h=JSON.parse(n.data);a(u=>[...u,h])}catch{console.error("Failed to parse:",n.data)}}},[t]);const l=g=>{if(g.preventDefault(),!d.trim())return;if(c(d)){alert("Your message contains restricted words");return}const n={sender:t.username,receiver:t.username,text:d,timestamp:new Date().toISOString()};ws.readyState===WebSocket.OPEN&&ws.send(JSON.stringify(n)),a(h=>[...h,n]),i("")},c=g=>["porn","nude","xxx","sex","fuck","bullshit","asshole","bitch","cunt"].some(h=>g.toLowerCase().includes(h));return e.jsx("main",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("section",{className:"bg-white p-6 rounded shadow-md mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Message Allen"}),e.jsx("div",{ref:o,className:"space-y-3 max-h-96 overflow-y-auto p-4 border border-gray-200 rounded mb-4",children:r.length>0?r.map((g,n)=>e.jsx("div",{className:`py-2 ${g.sender===t.username?"text-right":"text-left"}`,children:e.jsxs("div",{className:`inline-block max-w-xs px-4 py-2 rounded ${g.sender===t.username?"bg-indigo-100 text-indigo-800":"bg-gray-100 text-gray-800"}`,children:[e.jsx("strong",{children:g.sender}),": ",g.text,e.jsx("small",{className:"block mt-1 text-xs text-gray-500",children:new Date(g.timestamp).toLocaleTimeString()})]})},n)):e.jsx("p",{className:"italic text-gray-500",children:"No messages yet."})}),e.jsxs("form",{onSubmit:l,className:"mt-4 flex items-end gap-2",children:[e.jsx("input",{type:"text",value:d,onChange:g=>i(g.target.value),placeholder:"Type your message...",className:"flex-grow px-4 py-2 border border-gray-300 rounded-md"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700",children:"Send"})]})]})})}function Ee(){const[t,r]=s.useState([]),[a,d]=s.useState(""),[i,o]=s.useState(!0),[l,c]=s.useState(null);s.useEffect(()=>{async function n(){try{const h=await fetch("/api/mentors");if(!h.ok)throw new Error("Failed to load mentors");const u=await h.json();r(u)}catch(h){c(h.message)}finally{o(!1)}}n()},[]);const g=async()=>{if(a.trim())try{const n=await fetch(`/api/mentors/country?country=${a}`);if(!n.ok)throw new Error("No mentors found");const h=await n.json();r(h.mentors||[])}catch{alert("No mentors found for that country.")}};return i?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading mentors..."})}):l?e.jsxs("div",{className:"text-red-500 text-center py-10",children:[e.jsx("p",{children:l}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-4 underline",children:"Try again"})]}):e.jsxs("main",{className:"max-w-6xl mx-auto p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Find a Mentor"}),e.jsxs("form",{onSubmit:n=>{n.preventDefault(),g()},className:"mb-8 flex gap-2 max-w-md mx-auto",children:[e.jsx("input",{type:"text",value:a,onChange:n=>d(n.target.value),placeholder:"Search mentors by country...",className:"w-full px-4 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-indigo-500 outline-none"}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"🔍"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.length>0?t.map((n,h)=>e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition",children:[e.jsx("h3",{className:"text-xl font-semibold",children:n.full_name}),e.jsx("p",{className:"text-indigo-600 font-medium mt-1",children:n.category}),e.jsx("p",{className:"mt-2 text-gray-700 line-clamp-2",children:n.expertise}),e.jsxs("p",{className:"mt-2 text-sm text-gray-500",children:["Experience: ",n.experience_years," years"]}),e.jsx(T,{to:`/mentors/${n.id}`,className:"mt-4 inline-block text-indigo-600 hover:text-indigo-800 hover:underline",children:"View Profile"})]},h)):e.jsx("p",{className:"italic text-gray-500 col-span-full text-center py-10",children:"No mentors found. Try searching by country or check back later."})})]})}function K({postId:t}){const[r,a]=s.useState([]),[d,i]=s.useState(""),[o,l]=s.useState(1),[c,g]=s.useState(!0),[n,h]=s.useState(!1),u=async m=>{const j=await(await fetch(`/api/comments/${t}?page=${m}`)).json();j.length===0&&g(!1),a(f=>[...f,...j])};s.useEffect(()=>{u(o)},[o]);const x=()=>{c&&!n&&l(m=>m+1)},b=async m=>{m.preventDefault();try{const v=await fetch(`/api/comments/${t}`,{method:"POST",body:JSON.stringify({content:d}),headers:{"Content-Type":"application/json"},credentials:"include"});if(v.ok){const j=await v.json();a([j,...r]),i("")}else{const j=await v.json().catch(()=>({detail:"Failed to submit comment"}));alert(`Failed to submit comment: ${j.detail||"Unknown error"}`)}}catch(v){console.error("Error submitting comment:",v),alert("Failed to submit comment: Network error")}};return e.jsxs("section",{className:"mt-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Comments"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[r.map(m=>e.jsxs("div",{className:"bg-gray-50 p-4 rounded border",children:[e.jsx("p",{className:"text-gray-800",children:m.content}),e.jsxs("small",{className:"text-sm text-gray-500",children:["— ",m.author," • ",new Date(m.created_at).toLocaleString()]})]},m.id)),c&&e.jsx("button",{onClick:x,className:"text-indigo-600 hover:text-indigo-800 underline text-sm",children:"Load More Comments"})]}),e.jsxs("form",{onSubmit:b,className:"space-y-4",children:[e.jsx("textarea",{value:d,onChange:m=>i(m.target.value),placeholder:"Add a comment...",className:"w-full px-4 py-2 border border-gray-300 rounded",required:!0}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded",children:"Submit"})]})]})}function Pe({user:t}){const{id:r}=q(),[a,d]=s.useState(null),[i,o]=s.useState(!1),[l,c]=s.useState(!1),g=R();s.useEffect(()=>{fetch(`/api/posts/${r}`).then(x=>x.json()).then(d).catch(x=>console.error("Error fetching post:",x))},[r]);const n=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${r}`,{method:"DELETE",credentials:"include"});if(!b.ok){const m=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(m.detail||"Something went wrong")}alert("Post deleted successfully!"),g("/dashboard")}catch(b){alert(b.message)}},h=()=>{g(`/posts/${r}/edit`)},u=async()=>{if(!t){alert("Please log in to save posts");return}c(!0);try{const x=await fetch(`/api/posts/${a.id}/save`,{method:"POST",credentials:"include"});if(x.ok){const b=await x.json();d(m=>({...m,user_saved:b.saved})),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(x){console.error("Error toggling save:",x),alert("Failed to toggle save")}finally{c(!1)}};return a?e.jsx("main",{role:"main",id:"main-content",className:"max-w-3l mx auto",children:e.jsxs("div",{className:"max-w-3xl mx-auto p-6 bg-white rounded shadow",children:[e.jsx("h1",{className:"text-2xl font-bold mb-4",children:a.title}),a.image_url&&e.jsx("img",{src:a.image_url,alt:a.title,className:"w-full h-auto rounded mb-4"}),e.jsx("div",{className:"mb-6 prose prose-lg max-w-none",dangerouslySetInnerHTML:{__html:a.content}}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-6",children:["By ",a.author_username," • ",new Date(a.created_at).toLocaleString()]}),e.jsxs("div",{className:"flex gap-4 mb-6",children:[t&&e.jsx("button",{onClick:u,disabled:l,className:`px-4 py-2 rounded transition ${a.user_saved?"bg-indigo-600 hover:bg-indigo-700 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700"} ${l?"opacity-50 cursor-not-allowed":""}`,children:l?"⏳":a.user_saved?"🔖 Saved":"📌 Save Post"}),e.jsx("button",{onClick:()=>o(!0),className:"px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"📤 Share Post"}),t&&(t.username===a.author_username||t.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:h,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition",children:"🖋️ Edit Post"}),e.jsx("button",{onClick:n,className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition",children:"🗑️ Delete Post"})]})]}),e.jsx(K,{postId:a.id}),e.jsx(W,{isOpen:i,onClose:()=>o(!1),post:a})]})}):e.jsx("p",{children:"Loading..."})}function Fe({post:t,user:r,showActions:a=!1,onPostUpdate:d}){const i=R(),[o,l]=s.useState(!1),[c,g]=s.useState(t),n=async()=>{if(window.confirm("Are you sure you want to delete this post?"))try{const b=await fetch(`/api/posts/${t.id}`,{method:"DELETE",credentials:"include"});if(!b.ok){const m=await b.json().catch(()=>({detail:"Failed to delete post"}));throw new Error(m.detail||"Something went wrong")}alert("Post deleted successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),window.location.reload()}catch(b){alert(b.message)}},h=()=>{i(`/posts/${t.id}/edit`)},u=async()=>{if(!r){alert("Please log in to save posts");return}l(!0);try{const x=await fetch(`/api/posts/${c.id}/save`,{method:"POST",credentials:"include"});if(x.ok){const b=await x.json();g(m=>({...m,user_saved:b.saved})),d&&d(c.id,{user_saved:b.saved}),alert(b.saved?"Post saved!":"Post unsaved!")}}catch(x){console.error("Error toggling save:",x),alert("Failed to toggle save")}finally{l(!1)}};return e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow",children:[e.jsx("h4",{className:"text-xl font-bold mb-2",children:t.title}),e.jsx("div",{className:"text-gray-700 mb-3 line-clamp-3",dangerouslySetInnerHTML:{__html:t.content.length>200?t.content.slice(0,200)+"...":t.content}}),t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full h-40 object-cover rounded mb-4"}),e.jsxs("small",{className:"block text-sm text-gray-500 mb-3",children:["By ",c.author_username," • ",new Date(c.created_at).toLocaleDateString()]}),e.jsxs("div",{className:"flex gap-2 items-center flex-wrap",children:[e.jsx("a",{href:`/posts/${c.id}`,className:"text-indigo-600 hover:underline",children:"View Post"}),r&&e.jsx("button",{onClick:u,disabled:o,className:`text-sm px-2 py-1 rounded transition ${c.user_saved?"text-indigo-600 bg-indigo-50":"text-gray-600 hover:text-indigo-600 hover:bg-indigo-50"} ${o?"opacity-50 cursor-not-allowed":""}`,children:o?"⏳":c.user_saved?"🔖 Saved":"📌 Save"}),a&&r&&(r.username===c.author_username||r.role==="admin")&&e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:h,className:"text-blue-600 hover:underline text-sm",children:"🖋️ Edit"}),e.jsx("button",{onClick:n,className:"text-red-600 hover:underline text-sm",children:"🗑️ Delete"})]})]})]})}function _e({initialPosts:t=[]}){const[r,a]=s.useState(t),[d,i]=s.useState(!0),[o,l]=s.useState(1),[c,g]=s.useState(!1),[n,h]=s.useState(""),[u,x]=s.useState(""),[b,m]=s.useState("latest"),v=async()=>{if(!(!d||c)){g(!0);try{const f=await(await fetch(`/api/posts?page=${o}&limit=6`)).json();f.length===0&&i(!1),a(p=>[...p,...f]),l(p=>p+1)}catch{console.error("Failed to load more posts")}finally{g(!1)}}};return useEffect(()=>{const j=()=>{window.innerHeight+window.scrollY>=document.body.offsetHeight-500&&d&&!c&&v()};return window.addEventListener("scroll",j),()=>window.removeEventListener("scroll",j)},[d,c]),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(j=>e.jsx(Fe,{post:j},j.id))}),c&&e.jsx("p",{className:"text-center",children:"Loading more posts..."}),!d&&e.jsx("p",{className:"text-center text-gray-500 italic",children:"No more posts to load"})]})}function $e({user:t}){const[r,a]=s.useState([]),[d,i]=s.useState(!0),[o,l]=s.useState(null),{announcePolite:c,announceAssertive:g}=U();return s.useEffect(()=>{if(!(t!=null&&t.username)){i(!1);return}i(!0),l(null),fetch(`/api/activity/${t.username}`).then(n=>{if(!n.ok)throw new Error("Failed to fetch activities");return n.json()}).then(n=>{a(n||[]),i(!1),c(`Activity feed loaded with ${(n==null?void 0:n.length)||0} activities`)}).catch(n=>{console.error("Error fetching activities:",n),l(n.message),a([]),i(!1),g("Failed to load activity feed")})},[t]),t?d?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsx("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(n=>e.jsx("div",{className:"bg-gray-200 h-16 rounded"},n))})]})}):o?e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading activities: ",o]}),e.jsx("button",{onClick:()=>window.location.reload(),className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Your Activity"}),r.length>0?e.jsx("div",{className:"space-y-4",children:r.map((n,h)=>e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-indigo-500 hover:shadow-md transition-shadow",children:[e.jsx("p",{className:"text-gray-900 dark:text-gray-100 font-medium",children:n.action}),e.jsx("small",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(n.timestamp).toLocaleString()})]},h))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:"📊"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:"No activity yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"Start creating posts and interacting to see your activity here!"}),e.jsx("a",{href:"/dashboard",className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Go to Dashboard"})]})]})}):e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your activity feed."})})})}function H({user:t}){const r=Z(),[a,d]=s.useState(()=>r.pathname==="/archived-chats"?"archived-chats":"saved-posts"),[i,o]=s.useState([]),[l,c]=s.useState([]),[g,n]=s.useState(!0),[h,u]=s.useState(null),[x,b]=s.useState(new Set),m=R(),{announcePolite:v,announceAssertive:j}=U();s.useEffect(()=>{if(!t){n(!1);return}f()},[t]);const f=async()=>{try{n(!0),u(null);let y=[],w=[];const N=await fetch("/api/saved-posts",{credentials:"include"});N.ok&&(y=await N.json(),o(y));const E=await fetch("/api/archived-chats",{credentials:"include"});E.ok&&(w=await E.json(),c(w)),v(`Archives loaded with ${y.length} saved posts and ${w.length} archived chats`)}catch(y){console.error("Error fetching archives:",y),u(y.message),j("Failed to load archives")}finally{n(!1)}},p=async(y,w)=>{try{if(w==="post")if((await fetch(`/api/posts/${y}/save`,{method:"POST",credentials:"include"})).ok)o(E=>E.filter(L=>L.id!==y)),v("Post removed from archives");else throw new Error("Failed to remove post from archives");else w==="chat"&&(c(N=>N.filter(E=>E.id!==y)),v("Chat removed from archives"))}catch(N){console.error("Error removing from archives:",N),j("Failed to remove item from archives"),alert("Failed to remove item from archives")}},k=async()=>{if(!(x.size===0||!window.confirm(`Remove ${x.size} items from archives?`)))try{for(const w of x){const N=[...i,...l].find(E=>`${E.type||"post"}-${E.id}`===w);N&&await p(N.id,N.type||"post")}b(new Set),v(`${x.size} items removed from archives`)}catch{j("Failed to remove some items")}},P=(y,w)=>{const N=`${w}-${y}`,E=new Set(x);E.has(N)?E.delete(N):E.add(N),b(E)},D=()=>{const w=(a==="saved-posts"?i:l).map(N=>`${N.type||"post"}-${N.id}`);b(new Set(w))},$=()=>{b(new Set)};if(!t)return e.jsx("div",{className:"max-w-6xl mx-auto p-6",children:e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsx("p",{className:"text-yellow-800",children:"Please log in to view your archives."})})});if(g)return e.jsx("div",{className:"max-w-6xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Archives"}),e.jsx("div",{className:"animate-pulse space-y-4",children:[1,2,3].map(y=>e.jsx("div",{className:"bg-gray-200 h-24 rounded"},y))})]})});if(h)return e.jsx("div",{className:"max-w-6xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Archives"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[e.jsxs("p",{className:"text-red-800",children:["Error loading archives: ",h]}),e.jsx("button",{onClick:f,className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition",children:"Try Again"})]})]})});const S=a==="saved-posts"?i:l,C=x.size>0;return e.jsx("div",{className:"max-w-6xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-gray-100",children:"Archives"}),C&&e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400 self-center",children:[x.size," selected"]}),e.jsx("button",{onClick:k,className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition",children:"Remove Selected"}),e.jsx("button",{onClick:$,className:"px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition",children:"Clear"})]})]}),e.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 mb-6",children:e.jsxs("nav",{className:"flex space-x-8",children:[e.jsxs("button",{onClick:()=>d("saved-posts"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${a==="saved-posts"?"border-indigo-500 text-indigo-600 dark:text-indigo-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:["📌 Saved Posts (",i.length,")"]}),e.jsxs("button",{onClick:()=>d("archived-chats"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${a==="archived-chats"?"border-indigo-500 text-indigo-600 dark:text-indigo-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"}`,children:["💬 Chat History (",l.length,")"]})]})}),S.length>0&&e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:D,className:"text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300",children:"Select All"}),C&&e.jsx("button",{onClick:$,className:"text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300",children:"Clear Selection"})]}),e.jsxs("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[S.length," ",a==="saved-posts"?"saved posts":"archived chats"]})]}),S.length>0?e.jsx("div",{className:"space-y-4",children:S.map(y=>e.jsx(De,{item:y,type:a==="saved-posts"?"post":"chat",isSelected:x.has(`${y.type||(a==="saved-posts"?"post":"chat")}-${y.id}`),onToggleSelect:()=>P(y.id,y.type||(a==="saved-posts"?"post":"chat")),onRemove:()=>p(y.id,y.type||(a==="saved-posts"?"post":"chat")),onView:()=>{a==="saved-posts"?m(`/posts/${y.id}`):alert("Chat viewing not implemented yet")}},y.id))}):e.jsx(Le,{activeTab:a})]})})}function De({item:t,type:r,isSelected:a,onToggleSelect:d,onRemove:i,onView:o}){return e.jsx("div",{className:`bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow ${a?"ring-2 ring-indigo-500":""}`,children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex items-start space-x-3 flex-1",children:[e.jsx("input",{type:"checkbox",checked:a,onChange:d,className:"mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:t.title}),r==="post"&&e.jsxs(e.Fragment,{children:[t.image_url&&e.jsx("img",{src:t.image_url,alt:t.title,className:"w-full h-32 object-cover rounded mb-3"}),e.jsx("div",{className:"text-gray-700 dark:text-gray-300 mb-3 prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:t.content.length>150?t.content.slice(0,150)+"...":t.content}}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{children:["By ",t.author_username," • ",new Date(t.created_at).toLocaleDateString()]}),e.jsxs("span",{children:["Saved ",new Date(t.saved_at).toLocaleDateString()]})]})]}),r==="chat"&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-700 dark:text-gray-300 mb-3",children:t.preview}),e.jsxs("div",{className:"flex justify-between items-center text-sm text-gray-500 dark:text-gray-400",children:[e.jsxs("span",{children:["With ",t.participant]}),e.jsxs("span",{children:["Archived ",new Date(t.archived_at).toLocaleDateString()]})]})]})]})]}),e.jsxs("div",{className:"flex space-x-2 ml-4",children:[e.jsx("button",{onClick:o,className:"px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm transition",children:"View"}),e.jsx("button",{onClick:i,className:"px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition",title:"Remove from Archives",children:"Remove"})]})]})})}function Le({activeTab:t}){return e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-6xl mb-4",children:t==="saved-posts"?"📌":"💬"}),e.jsx("h3",{className:"text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2",children:t==="saved-posts"?"No saved posts yet":"No archived chats yet"}),e.jsx("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:t==="saved-posts"?"Start saving posts you want to read later!":"Chat conversations will appear here when archived."}),t==="saved-posts"&&e.jsx("button",{onClick:()=>window.location.href="/dashboard",className:"inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition",children:"Browse Posts"})]})}function Ae({postId:t}){const[r,a]=s.useState(""),[d,i]=s.useState(""),[o,l]=s.useState(null),[c,g]=s.useState(null),[n,h]=s.useState(!1),[u,x]=s.useState(""),{id:b}=q(),m=R();s.useEffect(()=>{fetch(`/api/posts/${t}`).then(f=>f.json()).then(f=>{a(f.title),i(f.content),g(f.image_url)})},[t]);const v=f=>{const p=f.target.files[0];if(p){l(p);const k=new FileReader;k.onloadend=()=>g(k.result),k.readAsDataURL(p)}},j=async f=>{f.preventDefault(),h(!0),x(""),m(`/posts/${b}`);const p=new FormData;p.append("title",r),p.append("content",d),o&&p.append("image",o);try{if(!(await fetch(`/api/posts/${t}`,{method:"POST",body:p,credentials:"include"})).ok)throw new Error("Update failed");alert("Post updated!"),window.location.href=`/posts/${t}`}catch(k){x(k.message||"Error updating post")}finally{h(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Edit Your Post"}),u&&e.jsx("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:u}),e.jsxs("form",{onSubmit:j,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",value:r,onChange:f=>a(f.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"content",className:"block text-sm font-medium mb-1",children:"Content"}),e.jsx("textarea",{id:"content",rows:"5",value:d,onChange:f=>i(f.target.value),required:!0,disabled:n,className:"w-full px-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-indigo-500 disabled:bg-gray-100"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"image",className:"block text-sm font-medium mb-1",children:"Upload New Image (optional)"}),e.jsx("input",{id:"image",type:"file",accept:"image/*",onChange:v,disabled:n,className:"w-full px-2 py-1 border border-gray-300 rounded disabled:bg-gray-100"})]}),c&&e.jsx("img",{src:c,alt:"Preview",className:"mt-2 w-full h-48 object-cover rounded"}),e.jsx("button",{type:"submit",disabled:n,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${n?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:n?"Saving...":"Save Changes"})]})]})}function Re(){const[t,r]=s.useState(0),[a,d]=s.useState(""),{announcePolite:i,announceAssertive:o}=U(),l=()=>{const h=a||`Button clicked ${t+1} times`;i(h),r(u=>u+1)},c=()=>{o(a||"This is an urgent announcement!")},g=h=>{h.preventDefault(),i("Form submitted successfully")},n=()=>{o("Error: Something went wrong!")};return e.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100",children:"Announcer Demo - Screen Reader Accessibility"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900 p-4 rounded-lg",children:[e.jsx("h3",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:"What is the Announcer?"}),e.jsx("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:"The Announcer component provides accessibility announcements for screen readers. It uses ARIA live regions to communicate dynamic content changes to visually impaired users. The announcements are invisible but will be read aloud by screen readers."})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"custom-message",className:"block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300",children:"Custom Message (optional):"}),e.jsx("input",{id:"custom-message",type:"text",value:a,onChange:h=>d(h.target.value),placeholder:"Enter a custom message to announce...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Polite Announcements"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"These don't interrupt current screen reader speech"}),e.jsxs("button",{onClick:l,className:"w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:["🔊 Polite Announcement (Counter: ",t,")"]}),e.jsx("button",{onClick:()=>i("Page content has been updated"),className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition",children:"📄 Content Updated"}),e.jsx("button",{onClick:()=>i("New post has been added to the feed"),className:"w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition",children:"➕ New Content Added"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100",children:"Assertive Announcements"}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"These interrupt current speech for urgent messages"}),e.jsx("button",{onClick:c,className:"w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition",children:"⚠️ Urgent Announcement"}),e.jsx("button",{onClick:n,className:"w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition",children:"🚨 Error Announcement"}),e.jsx("button",{onClick:()=>o("Form validation failed. Please check your inputs."),className:"w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition",children:"❌ Validation Error"})]})]}),e.jsxs("div",{className:"border-t pt-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-4",children:"Form Interaction Demo"}),e.jsxs("form",{onSubmit:g,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"demo-input",className:"block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300",children:"Demo Input:"}),e.jsx("input",{id:"demo-input",type:"text",placeholder:"Type something...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),e.jsx("button",{type:"submit",className:"px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"Submit Form (Announces Success)"})]})]}),e.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[e.jsx("h4",{className:"font-semibold text-gray-900 dark:text-gray-100 mb-2",children:"How to Test:"}),e.jsxs("ul",{className:"text-sm text-gray-700 dark:text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Turn on a screen reader (NVDA, JAWS, VoiceOver, etc.)"}),e.jsx("li",{children:"• Click the buttons above"}),e.jsx("li",{children:"• Listen for the announcements"}),e.jsx("li",{children:"• Polite announcements wait for current speech to finish"}),e.jsx("li",{children:"• Assertive announcements interrupt current speech"})]})]})]})]})})}function Ie({user:t}){const{postId:r}=q(),[a,d]=s.useState(null),[i,o]=s.useState(""),[l,c]=s.useState(""),[g,n]=s.useState(null),[h,u]=s.useState(!0),x=R(),b=s.useRef(null),m=()=>{const p=document.createElement("input");p.setAttribute("type","file"),p.setAttribute("accept","image/*"),p.click(),p.onchange=async()=>{const k=p.files[0];if(k){const P=new FormData;P.append("image",k);try{const D=await fetch("/api/upload-image",{method:"POST",body:P});if(D.ok){const S=(await D.json()).url,C=b.current.getEditor(),y=C.getSelection();C.insertEmbed(y.index,"image",S)}else{const $=new FileReader;$.onload=S=>{const C=b.current.getEditor(),y=C.getSelection();C.insertEmbed(y.index,"image",S.target.result)},$.readAsDataURL(k)}}catch(D){console.error("Image upload failed:",D);const $=new FileReader;$.onload=S=>{const C=b.current.getEditor(),y=C.getSelection();C.insertEmbed(y.index,"image",S.target.result)},$.readAsDataURL(k)}}}},v=s.useMemo(()=>({toolbar:{container:[[{header:[1,2,3,4,5,6,!1]}],[{font:[]},{size:["small",!1,"large","huge"]}],["bold","italic","underline","strike"],[{color:[]},{background:[]}],[{script:"sub"},{script:"super"}],[{list:"ordered"},{list:"bullet"},{list:"check"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"},{align:[]}],["link","image","video","formula"],["blockquote","code-block"],["clean"]],handlers:{image:m}},clipboard:{matchVisual:!1},history:{delay:2e3,maxStack:500,userOnly:!0}}),[]),j=["header","font","size","bold","italic","underline","strike","color","background","script","list","bullet","check","indent","direction","align","link","image","video","formula","blockquote","code-block","clean"];s.useEffect(()=>{async function p(){const k=await fetch(`/api/posts/${r}`,{credentials:"include"});if(!k.ok){x("/dashboard");return}const P=await k.json();if(P.author_username!==(t==null?void 0:t.username)&&(t==null?void 0:t.role)!=="admin"){x("/dashboard");return}o(P.title),c(P.content),d(P),u(!1)}t&&p()},[r,t,x]);const f=async p=>{p.preventDefault();const k=new FormData;k.append("title",i),k.append("content",l),g&&k.append("post_image",g);try{const P=await fetch(`/api/posts/${r}`,{method:"POST",body:k,credentials:"include"});if(P.ok)alert("Post updated successfully!"),localStorage.setItem("postsUpdated",Date.now().toString()),x(`/posts/${r}`);else{const D=await P.json().catch(()=>({detail:"Failed to update post"}));alert(`Failed to update post: ${D.detail||"Unknown error"}`)}}catch(P){console.error("Error updating post:",P),alert(`Network error – could not update post: ${P.message}`)}};return h?e.jsx("div",{className:"flex justify-center items-center h-64",children:e.jsx("p",{children:"Loading post..."})}):e.jsxs("main",{className:"container mx-auto py-10 px-4 max-w-3xl",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Edit Your Post"}),e.jsxs("form",{onSubmit:f,encType:"multipart/form-data",className:"space-y-6",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:"Title"}),e.jsx("input",{id:"title",type:"text",name:"title",value:i,onChange:p=>o(p.target.value),required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["Content",e.jsxs("span",{className:"text-xs text-gray-500 ml-2",children:["(",l.replace(/<[^>]*>/g,"").length," characters)"]})]}),e.jsx("div",{className:"border border-gray-300 rounded focus-within:ring-2 focus-within:ring-indigo-500",children:e.jsx(Y,{ref:b,theme:"snow",value:l,onChange:c,modules:v,formats:j,placeholder:"Edit your post content here...",style:{minHeight:"300px",backgroundColor:"white"}})})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{htmlFor:"post_image",className:"block text-sm font-medium text-gray-700 mb-1",children:"Update Image (Optional)"}),e.jsx("input",{id:"post_image",type:"file",name:"post_image",accept:"image/*",onChange:p=>n(p.target.files[0]),className:"w-full px-3 py-2 border border-gray-300 rounded-md"})]}),e.jsx("button",{type:"submit",className:"px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-semibold rounded-md transition",children:"Update Post"})]})]})}function Me(){const[t,r]=s.useState(""),[a,d]=s.useState(""),[i,o]=s.useState(""),[l,c]=s.useState("/static/avatars/default.png"),[g,n]=s.useState(!1),h=x=>{const b=x.target.files[0];if(b){const m=new FileReader;m.onload=()=>c(m.result),m.readAsDataURL(b)}},u=async x=>{x.preventDefault(),n(!0);const b=new FormData;b.append("full_name",t),b.append("bio",a),b.append("github_username",i),x.target.avatar.files.length>0&&b.append("avatar",x.target.avatar.files[0]);try{console.log("Submitting profile data:",{fullName:t,bio:a,github:i});const m=await fetch("/create-profile",{method:"POST",credentials:"include",body:b});if(console.log("Response status:",m.status),m.ok)console.log("Profile created successfully"),alert("Profile saved successfully!"),window.location.href="/dashboard";else{const v=await m.text();console.error("Profile creation failed:",v),alert(`Update failed: ${v}`)}}catch(m){console.error("Profile creation error:",m),alert(`Error updating profile: ${m.message}`)}finally{n(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),e.jsxs("form",{onSubmit:u,className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{type:"text",value:t,onChange:x=>r(x.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{rows:"4",value:a,onChange:x=>d(x.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"GitHub Username"}),e.jsx("input",{type:"text",value:i,onChange:x=>o(x.target.value),className:"w-full px-4 py-2 border rounded"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:l,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:h,accept:"image/*"})]}),e.jsx("button",{type:"submit",className:"mt-4 w-full py-2 px-4 rounded bg-indigo-600 hover:bg-indigo-700 text-white",disabled:g,children:g?"Saving...":"Save Profile"})]})]})}function Te({user:t}){const[r,a]=s.useState((t==null?void 0:t.full_name)||""),[d,i]=s.useState((t==null?void 0:t.bio)||""),[o,l]=s.useState((t==null?void 0:t.github_username)||""),[c,g]=s.useState((t==null?void 0:t.avatar_url)||"/static/avatars/default.png"),[n,h]=s.useState(!1),[u,x]=s.useState(!1),[b,m]=s.useState(""),v=f=>{const p=f.target.files[0];if(p){const k=new FileReader;k.onload=()=>g(k.result),k.readAsDataURL(p)}},j=async f=>{f.preventDefault(),x(!0),m(""),h(!1);const p=new FormData;p.append("full_name",r),p.append("bio",d),p.append("github_username",o),image&&p.append("image",image),f.target.avatar.files.length>0&&p.append("avatar",f.target.avatar.files[0]);try{if(!(await fetch("/create-profile",{method:"POST",credentials:"include",body:p})).ok)throw new Error("Failed to update profile");h(!0),setTimeout(()=>{window.location.href="/dashboard"},1e3)}catch(k){m(k.message||"Error updating profile")}finally{x(!1)}};return e.jsxs("div",{className:"max-w-2xl mx-auto bg-white p-6 rounded shadow space-y-6",children:[e.jsx("h2",{className:"text-2xl font-bold",children:"Complete Your Profile"}),n&&e.jsx("div",{className:"bg-green-100 text-green-700 p-3 rounded",children:"✅ Profile updated successfully!"}),b&&e.jsxs("div",{className:"bg-red-100 text-red-700 p-3 rounded",children:["❌ ",b]}),e.jsxs("form",{onSubmit:j,className:"space-y-4",children:[e.jsx("form",{action:"/create-profile",method:"post",enctype:"multipart/form-data"}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"full_name",className:"block text-sm font-medium mb-1",children:"Full Name"}),e.jsx("input",{id:"full_name",type:"text",value:r,onChange:f=>a(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:u})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium mb-1",children:"Bio"}),e.jsx("textarea",{id:"bio",rows:"4",value:d,onChange:f=>i(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:u})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"github_username",className:"block text-sm font-medium mb-1",children:"GitHub Username (optional)"}),e.jsx("input",{id:"github_username",type:"text",value:o,onChange:f=>l(f.target.value),className:"w-full px-4 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-indigo-500",disabled:u})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium mb-1",children:"Avatar"}),e.jsx("img",{src:c,alt:"Current Avatar",className:"w-20 h-20 rounded-full object-cover mb-2"}),e.jsx("input",{type:"file",name:"avatar",onChange:v,accept:"image/*",disabled:u,ImageUpload:!0,onUpload:f=>setImage(f),className:"w-full px-2 py-1 border rounded"})]}),e.jsx("button",{type:"submit",disabled:u,className:`mt-4 w-full py-2 px-4 rounded text-white font-medium transition ${u?"bg-indigo-400 cursor-not-allowed":"bg-indigo-600 hover:bg-indigo-700"}`,children:u?"Saving Changes...":"Save Profile"})]})]})}function qe({user:t}){const[r,a]=s.useState(!1),[d,i]=s.useState(null);s.useEffect(()=>{const l=c=>{c.preventDefault(),i(c),a(!0)};return window.addEventListener("beforeinstallprompt",l),a(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone===!0||window.matchMedia("(prefers-app-theme: dark)").matches),()=>window.removeEventListener("beforeinstallprompt",l)},[]);const o=l=>{l.preventDefault(),d&&d.prompt&&d.prompt()};return e.jsxs("main",{className:"max-w-xl mx-auto p-6 bg-white rounded shadow-md mt-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Download RealHonest"}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Install as Web App"}),e.jsx("p",{className:"mb-4 text-gray-700",children:"RealHonest works great on all devices. You can install it like a native app."}),r?e.jsx("button",{type:"button",onClick:o,className:"w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white rounded transition",children:"💾 Install App"}):e.jsx("div",{className:"bg-blue-50 text-blue-700 p-3 rounded",children:e.jsx("p",{className:"text-sm",children:'To install, tap the browser menu and select "Add to Home Screen" or use the desktop app install option.'})})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📲 Native Mobile App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"RealHonest will soon be available on iOS and Android."}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white text-center rounded transition",children:"📱 Download for iOS (Coming Soon)"}),e.jsx("a",{href:"#",className:"block w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white text-center rounded transition",children:"📱 Download for Android (Coming Soon)"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"💻 Desktop App"}),e.jsx("p",{className:"mb-4 text-gray-700 text-sm",children:"Use RealHonest on desktop platforms today."}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:[e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Windows"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"macOS"}),e.jsx("a",{href:"#",className:"py-2 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center rounded transition",children:"Linux"})]})]}),e.jsxs("section",{className:"mb-8",children:[e.jsx("h3",{className:"text-lg font-semibold mb-2",children:"📱 Scan to Access"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Use this QR code to easily share the app with others."}),e.jsx("div",{className:"flex justify-center mb-6",children:e.jsx("img",{src:"https://quickchart.io/qr?text=http://localhost:5173&size=150",alt:"QR Code for RealHonest",className:"w-40 h-40 border p-2 rounded bg-white shadow-md"})})]}),e.jsx("div",{className:"text-center",children:e.jsx("a",{href:"/profile",className:"inline-block text-indigo-600 hover:underline",children:"← Back to Profile"})})]})}function Oe(){const[t,r]=s.useState(null),[a,d]=s.useState(!0);return s.useEffect(()=>{fetch("/api/user",{credentials:"include"}).then(i=>{if(i.ok)return i.json();throw new Error("Not authenticated")}).then(i=>r(i)).catch(()=>r(null)).finally(()=>d(!1))},[]),a?e.jsx("div",{className:"flex justify-center items-center h-screen bg-gray-50",children:e.jsx("p",{children:"Loading..."})}):e.jsx(le,{children:e.jsxs(ee,{children:[e.jsx(F,{path:"/",element:e.jsx(_,{user:t,children:e.jsx(be,{user:t})})}),e.jsx(F,{path:"/dashboard",element:e.jsx(_,{user:t,children:e.jsx(je,{user:t})})}),e.jsx(F,{path:"/login",element:e.jsx(_,{user:t,children:e.jsx(ve,{onLogin:r})})}),e.jsx(F,{path:"/signup",element:e.jsx(_,{user:t,children:e.jsx(we,{onLogin:r})})}),e.jsx(F,{path:"/posts/:id",element:e.jsx(_,{user:t,children:e.jsx(Pe,{user:t})})}),e.jsx(F,{path:"/posts/:postId/edit",element:e.jsx(_,{user:t,children:e.jsx(Ie,{user:t})})}),e.jsx(F,{path:"/register-mentor",element:e.jsx(_,{user:t,children:e.jsx(Ne,{user:t})})}),e.jsx(F,{path:"/mentors/:mentorId",element:e.jsx(_,{user:t,children:e.jsx(ke,{user:t})})}),e.jsx(F,{path:"/mentors",element:e.jsx(_,{user:t,children:e.jsx(Ee,{user:t})})}),e.jsx(F,{path:"/chat",element:e.jsx(_,{user:t,children:e.jsx(Se,{user:t})})}),e.jsx(F,{path:"/dm/:username",element:e.jsx(_,{user:t,children:e.jsx(Ce,{user:t})})}),e.jsx(F,{path:"/profile",element:e.jsx(_,{user:t,children:e.jsx(Me,{user:t})})}),e.jsx(F,{path:"/activity",element:e.jsx(_,{user:t,children:e.jsx($e,{user:t})})}),e.jsx(F,{path:"/archives",element:e.jsx(_,{user:t,children:e.jsx(H,{user:t})})}),e.jsx(F,{path:"/announcer-demo",element:e.jsx(_,{user:t,children:e.jsx(Re,{})})}),e.jsx(F,{path:"/posts",elements:e.jsx(_,{user:t,children:e.jsx(_e,{user:t})})}),e.jsx(F,{path:"/editpostform",element:e.jsx(_,{user:t,children:e.jsx(Ae,{})})}),e.jsx(F,{path:"/profile",element:e.jsx(_,{children:e.jsx(Te,{user:t})})}),e.jsx(F,{path:"/download",element:e.jsx(_,{children:e.jsx(qe,{user:t})})}),e.jsx(F,{path:"/saved-posts",element:e.jsx(_,{user:t,children:e.jsx(H,{user:t})})}),e.jsx(F,{path:"/archived-chats",element:e.jsx(_,{user:t,children:e.jsx(H,{user:t})})}),e.jsx(F,{path:"/comments",element:e.jsx(_,{children:e.jsx(K,{user:t})})})]})})}const Ue=B.createRoot(document.getElementById("root"));Ue.render(e.jsx(te,{children:e.jsx(Oe,{})}));"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/service-worker.js").then(t=>console.log("Service Worker registered:",t.scope)).catch(t=>console.error("Service Worker registration failed:",t))});
