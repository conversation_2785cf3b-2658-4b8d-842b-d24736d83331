// src/components/SavedPosts.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

export default function SavedPosts({ user }) {
  const [savedPosts, setSavedPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    fetchSavedPosts();
  }, [user]);

  const fetchSavedPosts = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/saved-posts', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to load saved posts');
      }

      const data = await response.json();
      setSavedPosts(data);
    } catch (err) {
      console.error('Error fetching saved posts:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUnsave = async (postId) => {
    try {
      const response = await fetch(`/api/posts/${postId}/save`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        // Remove the post from the saved posts list
        setSavedPosts(prev => prev.filter(post => post.id !== postId));
      } else {
        throw new Error('Failed to unsave post');
      }
    } catch (err) {
      console.error('Error unsaving post:', err);
      alert('Failed to unsave post');
    }
  };

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">Please log in to view your saved posts.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Saved Posts</h2>
          <div className="animate-pulse space-y-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-gray-200 h-32 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Saved Posts</h2>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading saved posts: {error}</p>
            <button 
              onClick={fetchSavedPosts} 
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Saved Posts</h2>
        
        {savedPosts.length > 0 ? (
          <div className="space-y-6">
            {savedPosts.map((post) => (
              <div 
                key={post.id} 
                className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">{post.title}</h3>
                  <button
                    onClick={() => handleUnsave(post.id)}
                    className="text-indigo-600 hover:text-red-600 transition-colors p-2 rounded hover:bg-red-50"
                    title="Remove from saved"
                  >
                    🔖
                  </button>
                </div>
                
                {post.image_url && (
                  <img 
                    src={post.image_url} 
                    alt={post.title} 
                    className="w-full h-48 object-cover rounded mb-4"
                  />
                )}
                
                <div
                  className="text-gray-700 dark:text-gray-300 mb-4 prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: post.content.length > 200 ? post.content.slice(0, 200) + "..." : post.content
                  }}
                />
                
                <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>By {post.author_username} • {new Date(post.created_at).toLocaleDateString()}</span>
                  <span>Saved {new Date(post.saved_at).toLocaleDateString()}</span>
                </div>
                
                <div className="mt-4 flex gap-2">
                  <button
                    onClick={() => navigate(`/posts/${post.id}`)}
                    className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition"
                  >
                    Read Full Post
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📌</div>
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">No saved posts yet</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">Start saving posts you want to read later!</p>
            <button 
              onClick={() => navigate('/dashboard')}
              className="inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition"
            >
              Browse Posts
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
