// src/components/Layout.jsx
import React from 'react';
import Navbar from './Navbar.jsx';
import SkipToContent from './SkipToContent.jsx';
import DropdownMenu from './DropdownMenu.jsx';
import { Link } from 'react-router-dom';

export default function Layout({ user, children }) {
  const [darkMode, setDarkMode] = React.useState(false);

  // Initialize dark mode from server or localStorage
  React.useEffect(() => {
    const initializeDarkMode = async () => {
      let initialDarkMode = false;

      // Try to get preference from server first (for logged-in users)
      if (user) {
        try {
          const response = await fetch('/api/user/dark-mode', {
            credentials: 'include'
          });
          if (response.ok) {
            const data = await response.json();
            initialDarkMode = data.dark_mode;
          }
        } catch (error) {
          console.log('Could not fetch dark mode preference from server');
        }
      }

      // Fall back to localStorage or system preference
      if (!user) {
        const localDarkMode = localStorage.getItem('darkMode');
        initialDarkMode = localDarkMode === 'true' ||
                         (localDarkMode === null && window.matchMedia('(prefers-color-scheme: dark)').matches);
      }

      setDarkMode(initialDarkMode);
    };

    initializeDarkMode();
  }, [user]);

  // Apply dark mode and sync with backend
  React.useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Update localStorage
    localStorage.setItem('darkMode', darkMode ? 'true' : 'false');

    // Save to backend if user is logged in
    if (user) {
      fetch('/api/user/dark-mode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({ dark_mode: darkMode })
      }).catch(error => {
        console.log('Could not save dark mode preference to server:', error);
      });
    }
  }, [darkMode, user]);

  return (
    <div className="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300">
      {/* Skip to Content */}
      <SkipToContent />

      {/* Header / Nav */}
      <header className="bg-white dark:bg-gray-800 shadow-sm p-4 flex justify-between items-center transition-colors duration-300">
        <div className="flex items-center space-x-2">
          <svg className="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
          </svg>
          <h1 className="text-xl font-bold">RealHonest – get real, be real</h1>
        </div>

        <nav className="space-x-4 hidden md:flex items-center">
          <a href="/" className="hover:text-indigo-600 transition">🏠 Home</a>
          <a href="/dashboard" className="hover:text-indigo-600 transition">Dashboard</a>
          <a href="/mentors" className="hover:text-indigo-600 transition">Mentors</a>

          {user && (
            <DropdownMenu
              trigger={
                <button
                  type="button"
                  aria-label="User menu"
                  className="focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-full ml-4"
                >
                  {user.avatar_url && user.avatar_url !== "/static/avatars/default.png" && user.avatar_url.includes('/static/avatars/') ? (
                    <img
                      src={user.avatar_url}
                      alt="User avatar"
                      className="w-8 h-8 rounded-full border-2 border-indigo-500 hover:border-indigo-700 transition object-cover"
                      onError={(e) => {
                        // If image fails to load, hide it and show initials
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div
                    className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 font-semibold border-2 border-indigo-500 hover:border-indigo-700 transition"
                    style={{ display: (user.avatar_url && user.avatar_url !== "/static/avatars/default.png" && user.avatar_url.includes('/static/avatars/')) ? 'none' : 'flex' }}
                  >
                    {user.username?.charAt(0).toUpperCase() || '?'}
                  </div>
                </button>
              }
              className="right-0 mt-2 w-48 z-50"
            >
              <Link to="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700">
                🧾 Settings
              </Link>
              <Link to="/archived-chats" className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700">
                📁 Archived Chats
              </Link>
              <Link to="/download" className="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-700">
                📲 Download App
              </Link>
              <button
                type="button"
                className="w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-900"
                onClick={async () => {
                  const confirmed = window.confirm("Are you sure you want to delete your account?");
                  if (confirmed) {
                    try {
                      const res = await fetch('/api/user', {
                        method: 'DELETE',
                        credentials: 'include'
                      });
                      if (res.ok) {
                        window.location.href = '/logout';
                      } else {
                        alert('Failed to delete account');
                      }
                    } catch (err) {
                      alert('Error deleting account');
                    }
                  }
                }}
              >
                ❌ Delete Account
              </button>
            </DropdownMenu>
          )}

          <button
            onClick={() => setDarkMode(!darkMode)}
            aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
            className="ml-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 rounded-md px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {darkMode ? "🌞 Light Mode" : "🌙 Dark Mode"}
          </button>
          <a href="/login" className="ml-4 hover:text-red-600 transition">🚪 Login</a>
          <a href="/logout" className="ml-4 hover:text-red-600 transition">🚪 Logout</a>
        </nav>
      </header>

      <main className="flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6">
        {/* Sidebar */}
        {user && ( 
        <aside className="md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sticky top-6 transition-colors duration-300">
          <nav className="space-y-2">
            <a href="/" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">🏠 Home</a>
            <a href="/dashboard" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">📊 Dashboard</a>
            <a href="/saved-posts" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">🔖 Saved Posts</a>
            <a href="/activity" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">📈 Activity</a>
            <a href="/announcer-demo" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">🔊 Accessibility Demo</a>
            <a href="/profile" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">⚙️ Profile</a>
            <a href="/chat" className="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">💬 Chat</a>
            <a href="/logout" className="block py-2 px-3 hover:bg-red-100 dark:hover:bg-red-900 rounded text-red-600 dark:text-red-400 transition-colors">🚪 Logout</a>
          </nav>
        </aside>
      )}

        {/* Main Content */}
        <section id="main-content" className="flex-grow">
          {children}
        </section>
      </main>

      {/* Footer */}
      <footer className="py-6 px-4 text-center text-gray-500 text-sm bg-white border-t mt-auto">
        &copy; {new Date().getFullYear()} RealHonest – get real, be real. All rights reserved.
      </footer>
    </div>
  );
}