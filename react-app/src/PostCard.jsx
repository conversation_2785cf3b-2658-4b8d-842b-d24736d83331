// src/components/PostCard.jsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export default function PostCard({ post, user, showActions = false, onPostUpdate }) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [postState, setPostState] = useState(post);

  const handleDelete = async () => {
    const confirmed = window.confirm("Are you sure you want to delete this post?");
    if (!confirmed) return;

    try {
      const res = await fetch(`/api/posts/${post.id}`, {
        method: "DELETE",
        credentials: "include"
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({ detail: "Failed to delete post" }));
        throw new Error(data.detail || "Something went wrong");
      }

      alert("Post deleted successfully!");
      // Trigger global post refresh
      localStorage.setItem('postsUpdated', Date.now().toString());
      window.location.reload(); // Simple refresh for now
    } catch (err) {
      alert(err.message);
    }
  };

  const handleEdit = () => {
    navigate(`/posts/${post.id}/edit`);
  };

  const handleSave = async () => {
    if (!user) {
      alert('Please log in to save posts');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/posts/${postState.id}/save`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setPostState(prev => ({
          ...prev,
          user_saved: data.saved
        }));
        // Call parent update function if provided
        if (onPostUpdate) {
          onPostUpdate(postState.id, { user_saved: data.saved });
        }
        alert(data.saved ? 'Post saved!' : 'Post unsaved!');
      }
    } catch (error) {
      console.error('Error toggling save:', error);
      alert('Failed to toggle save');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <h4 className="text-xl font-bold mb-2">{post.title}</h4>
      <div
        className="text-gray-700 mb-3 line-clamp-3"
        dangerouslySetInnerHTML={{ __html: post.content.length > 200 ? post.content.slice(0, 200) + '...' : post.content }}
      />
      {post.image_url && (
        <img 
          src={post.image_url} 
          alt={post.title}
          className="w-full h-40 object-cover rounded mb-4"
        />
      )}
      <small className="block text-sm text-gray-500 mb-3">
        By {postState.author_username} • {new Date(postState.created_at).toLocaleDateString()}
      </small>

      <div className="flex gap-2 items-center flex-wrap">
        <a href={`/posts/${postState.id}`} className="text-indigo-600 hover:underline">
          View Post
        </a>

        {/* Save button - only show if user is logged in */}
        {user && (
          <button
            onClick={handleSave}
            disabled={loading}
            className={`text-sm px-2 py-1 rounded transition ${
              postState.user_saved
                ? 'text-indigo-600 bg-indigo-50'
                : 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? '⏳' : (postState.user_saved ? '🔖 Saved' : '📌 Save')}
          </button>
        )}

        {/* Show edit/delete buttons if user owns the post or is admin */}
        {showActions && user && (user.username === postState.author_username || user.role === 'admin') && (
          <>
            <button
              onClick={handleEdit}
              className="text-blue-600 hover:underline text-sm"
            >
              🖋️ Edit
            </button>
            <button
              onClick={handleDelete}
              className="text-red-600 hover:underline text-sm"
            >
              🗑️ Delete
            </button>
          </>
        )}
      </div>
    </div>
  );
}