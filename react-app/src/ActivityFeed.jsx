// src/components/ActivityFeed.jsx
import React, { useEffect, useState } from 'react';
import { useAnnouncer } from './Announcer';

export default function ActivityFeed({ user }) {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { announcePolite, announceAssertive } = useAnnouncer();

  useEffect(() => {
    if (!user?.username) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    fetch(`/api/activity/${user.username}`)
      .then(res => {
        if (!res.ok) {
          throw new Error('Failed to fetch activities');
        }
        return res.json();
      })
      .then(data => {
        setActivities(data || []);
        setLoading(false);
        announcePolite(`Activity feed loaded with ${data?.length || 0} activities`);
      })
      .catch(err => {
        console.error('Error fetching activities:', err);
        setError(err.message);
        setActivities([]);
        setLoading(false);
        announceAssertive('Failed to load activity feed');
      });
  }, [user]);

  if (!user) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">Please log in to view your activity feed.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Your Activity</h2>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-gray-200 h-16 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Your Activity</h2>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading activities: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Your Activity</h2>

        {activities.length > 0 ? (
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div
                key={index}
                className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border-l-4 border-indigo-500 hover:shadow-md transition-shadow"
              >
                <p className="text-gray-900 dark:text-gray-100 font-medium">{activity.action}</p>
                <small className="text-sm text-gray-500 dark:text-gray-400">
                  {new Date(activity.timestamp).toLocaleString()}
                </small>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">No activity yet</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">Start creating posts and interacting to see your activity here!</p>
            <a
              href="/dashboard"
              className="inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition"
            >
              Go to Dashboard
            </a>
          </div>
        )}
      </div>
    </div>
  );
}