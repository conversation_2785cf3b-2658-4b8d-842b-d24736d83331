import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import CommentSection from './CommentSection';
import ShareModal from './ShareModal';

export default function PostDetail({ user }) {
  const { id } = useParams();
  const [post, setPost] = useState(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  
  useEffect(() => {
    fetch(`/api/posts/${id}`)
      .then(res => res.json())
      .then(setPost)
      .catch(err => console.error('Error fetching post:', err));
  }, [id]);

  const handleDelete = async () => {
    const confirmed = window.confirm("Are you sure you want to delete this post?");
    if (!confirmed) return;

    try {
      const res = await fetch(`/api/posts/${id}`, {
        method: "DELETE",
        credentials: "include"
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({ detail: "Failed to delete post" }));
        throw new Error(data.detail || "Something went wrong");
      }

      alert("Post deleted successfully!");
      navigate("/dashboard");
    } catch (err) {
      alert(err.message);
    }
  };

  const handleEdit = () => {
    navigate(`/posts/${id}/edit`);
  };

  const handleSave = async () => {
    if (!user) {
      alert('Please log in to save posts');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/posts/${post.id}/save`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setPost(prev => ({
          ...prev,
          user_saved: data.saved
        }));
        alert(data.saved ? 'Post saved!' : 'Post unsaved!');
      }
    } catch (error) {
      console.error('Error toggling save:', error);
      alert('Failed to toggle save');
    } finally {
      setLoading(false);
    }
  };

  if (!post) return <p>Loading...</p>;

  return (
    <main role="main" id="main-content" className='max-w-3l mx auto'>
    <div className="max-w-3xl mx-auto p-6 bg-white rounded shadow">
      <h1 className="text-2xl font-bold mb-4">{post.title}</h1>
      {post.image_url && (
        <img src={post.image_url} alt={post.title} className="w-full h-auto rounded mb-4" />
      )}
      <div
        className="mb-6 prose prose-lg max-w-none"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />
      
      <small className="block text-sm text-gray-500 mb-6">
        By {post.author_username} • {new Date(post.created_at).toLocaleString()}
      </small>

      {/* Social interaction buttons */}
      <div className="flex gap-4 mb-6">
        {/* Save button - only show if user is logged in */}
        {user && (
          <button
            onClick={handleSave}
            disabled={loading}
            className={`px-4 py-2 rounded transition ${
              post.user_saved
                ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? '⏳' : (post.user_saved ? '🔖 Saved' : '📌 Save Post')}
          </button>
        )}

        <button
          onClick={() => setShowShareModal(true)}
          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition"
        >
          📤 Share Post
        </button>

        {/* Edit and Delete buttons - only show if user owns the post or is admin */}
        {(user && (user.username === post.author_username || user.role === 'admin')) && (
          <>
            <button
              onClick={handleEdit}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition"
            >
              🖋️ Edit Post
            </button>
            <button
              onClick={handleDelete}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition"
            >
              🗑️ Delete Post
            </button>
          </>
        )}
      </div>

      {/* 💬 Comment Section */}
      <CommentSection postId={post.id} />

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        post={post}
      />
    </div>
    </main>
  );
}