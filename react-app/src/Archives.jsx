// src/components/Archives.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAnnouncer } from './Announcer';

export default function Archives({ user }) {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState(() => {
    // Set initial tab based on URL for backward compatibility
    if (location.pathname === '/archived-chats') return 'archived-chats';
    return 'saved-posts';
  });
  const [savedPosts, setSavedPosts] = useState([]);
  const [archivedChats, setArchivedChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedItems, setSelectedItems] = useState(new Set());
  const navigate = useNavigate();
  const { announcePolite, announceAssertive } = useAnnouncer();

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }
    fetchArchives();
  }, [user]);

  const fetchArchives = async () => {
    try {
      setLoading(true);
      setError(null);

      let savedData = [];
      let chatsData = [];

      // Fetch saved posts
      const savedResponse = await fetch('/api/saved-posts', {
        credentials: 'include'
      });

      if (savedResponse.ok) {
        savedData = await savedResponse.json();
        setSavedPosts(savedData);
      }

      // Fetch archived chats
      const chatsResponse = await fetch('/api/archived-chats', {
        credentials: 'include'
      });

      if (chatsResponse.ok) {
        chatsData = await chatsResponse.json();
        setArchivedChats(chatsData);
      }

      announcePolite(`Archives loaded with ${savedData.length} saved posts and ${chatsData.length} archived chats`);
    } catch (err) {
      console.error('Error fetching archives:', err);
      setError(err.message);
      announceAssertive('Failed to load archives');
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromArchives = async (itemId, itemType) => {
    try {
      if (itemType === 'post') {
        const response = await fetch(`/api/posts/${itemId}/save`, {
          method: 'POST',
          credentials: 'include'
        });

        if (response.ok) {
          setSavedPosts(prev => prev.filter(post => post.id !== itemId));
          announcePolite('Post removed from archives');
        } else {
          throw new Error('Failed to remove post from archives');
        }
      } else if (itemType === 'chat') {
        // Placeholder for chat removal
        setArchivedChats(prev => prev.filter(chat => chat.id !== itemId));
        announcePolite('Chat removed from archives');
      }
    } catch (err) {
      console.error('Error removing from archives:', err);
      announceAssertive('Failed to remove item from archives');
      alert('Failed to remove item from archives');
    }
  };

  const handleBulkRemove = async () => {
    if (selectedItems.size === 0) return;

    const confirmed = window.confirm(`Remove ${selectedItems.size} items from archives?`);
    if (!confirmed) return;

    try {
      for (const itemId of selectedItems) {
        const item = [...savedPosts, ...archivedChats].find(item => 
          `${item.type || 'post'}-${item.id}` === itemId
        );
        
        if (item) {
          await handleRemoveFromArchives(item.id, item.type || 'post');
        }
      }
      
      setSelectedItems(new Set());
      announcePolite(`${selectedItems.size} items removed from archives`);
    } catch (err) {
      announceAssertive('Failed to remove some items');
    }
  };

  const toggleItemSelection = (itemId, itemType) => {
    const key = `${itemType}-${itemId}`;
    const newSelected = new Set(selectedItems);
    
    if (newSelected.has(key)) {
      newSelected.delete(key);
    } else {
      newSelected.add(key);
    }
    
    setSelectedItems(newSelected);
  };

  const selectAll = () => {
    const currentItems = activeTab === 'saved-posts' ? savedPosts : archivedChats;
    const allKeys = currentItems.map(item => `${item.type || 'post'}-${item.id}`);
    setSelectedItems(new Set(allKeys));
  };

  const clearSelection = () => {
    setSelectedItems(new Set());
  };

  if (!user) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">Please log in to view your archives.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Archives</h2>
          <div className="animate-pulse space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-gray-200 h-24 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">Archives</h2>
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-800">Error loading archives: {error}</p>
            <button 
              onClick={fetchArchives} 
              className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const currentItems = activeTab === 'saved-posts' ? savedPosts : archivedChats;
  const hasSelection = selectedItems.size > 0;

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Archives</h2>
          
          {/* Bulk Actions */}
          {hasSelection && (
            <div className="flex gap-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 self-center">
                {selectedItems.size} selected
              </span>
              <button
                onClick={handleBulkRemove}
                className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition"
              >
                Remove Selected
              </button>
              <button
                onClick={clearSelection}
                className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition"
              >
                Clear
              </button>
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('saved-posts')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'saved-posts'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              📌 Saved Posts ({savedPosts.length})
            </button>
            <button
              onClick={() => setActiveTab('archived-chats')}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === 'archived-chats'
                  ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              💬 Chat History ({archivedChats.length})
            </button>
          </nav>
        </div>

        {/* Selection Controls */}
        {currentItems.length > 0 && (
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-2">
              <button
                onClick={selectAll}
                className="text-sm text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
              >
                Select All
              </button>
              {hasSelection && (
                <button
                  onClick={clearSelection}
                  className="text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300"
                >
                  Clear Selection
                </button>
              )}
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {currentItems.length} {activeTab === 'saved-posts' ? 'saved posts' : 'archived chats'}
            </p>
          </div>
        )}

        {/* Content */}
        {currentItems.length > 0 ? (
          <div className="space-y-4">
            {currentItems.map((item) => (
              <ArchiveItem
                key={item.id}
                item={item}
                type={activeTab === 'saved-posts' ? 'post' : 'chat'}
                isSelected={selectedItems.has(`${item.type || (activeTab === 'saved-posts' ? 'post' : 'chat')}-${item.id}`)}
                onToggleSelect={() => toggleItemSelection(item.id, item.type || (activeTab === 'saved-posts' ? 'post' : 'chat'))}
                onRemove={() => handleRemoveFromArchives(item.id, item.type || (activeTab === 'saved-posts' ? 'post' : 'chat'))}
                onView={() => {
                  if (activeTab === 'saved-posts') {
                    navigate(`/posts/${item.id}`);
                  } else {
                    // Navigate to chat when implemented
                    alert('Chat viewing not implemented yet');
                  }
                }}
              />
            ))}
          </div>
        ) : (
          <EmptyState activeTab={activeTab} />
        )}
      </div>
    </div>
  );
}

// Archive Item Component
function ArchiveItem({ item, type, isSelected, onToggleSelect, onRemove, onView }) {
  return (
    <div className={`bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow ${
      isSelected ? 'ring-2 ring-indigo-500' : ''
    }`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1">
          {/* Checkbox */}
          <input
            type="checkbox"
            checked={isSelected}
            onChange={onToggleSelect}
            className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
          />
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
              {item.title}
            </h3>
            
            {type === 'post' && (
              <>
                {item.image_url && (
                  <img 
                    src={item.image_url} 
                    alt={item.title} 
                    className="w-full h-32 object-cover rounded mb-3"
                  />
                )}
                <div
                  className="text-gray-700 dark:text-gray-300 mb-3 prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: item.content.length > 150 ? item.content.slice(0, 150) + "..." : item.content
                  }}
                />
                <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>By {item.author_username} • {new Date(item.created_at).toLocaleDateString()}</span>
                  <span>Saved {new Date(item.saved_at).toLocaleDateString()}</span>
                </div>
              </>
            )}
            
            {type === 'chat' && (
              <>
                <p className="text-gray-700 dark:text-gray-300 mb-3">{item.preview}</p>
                <div className="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400">
                  <span>With {item.participant}</span>
                  <span>Archived {new Date(item.archived_at).toLocaleDateString()}</span>
                </div>
              </>
            )}
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex space-x-2 ml-4">
          <button
            onClick={onView}
            className="px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded text-sm transition"
          >
            View
          </button>
          <button
            onClick={onRemove}
            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition"
            title="Remove from Archives"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
}

// Empty State Component
function EmptyState({ activeTab }) {
  return (
    <div className="text-center py-12">
      <div className="text-6xl mb-4">
        {activeTab === 'saved-posts' ? '📌' : '💬'}
      </div>
      <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
        {activeTab === 'saved-posts' ? 'No saved posts yet' : 'No archived chats yet'}
      </h3>
      <p className="text-gray-500 dark:text-gray-400 mb-6">
        {activeTab === 'saved-posts' 
          ? 'Start saving posts you want to read later!' 
          : 'Chat conversations will appear here when archived.'
        }
      </p>
      {activeTab === 'saved-posts' && (
        <button 
          onClick={() => window.location.href = '/dashboard'}
          className="inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition"
        >
          Browse Posts
        </button>
      )}
    </div>
  );
}
