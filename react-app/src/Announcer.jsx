import React, { useState, useContext, useCallback, useRef } from 'react';

// Screen reader only styles - visually hidden but accessible
const announcerStyle = {
  position: 'absolute',
  left: '-9999px',
  top: 'auto',
  width: '1px',
  height: '1px',
  overflow: 'hidden',
  clip: 'rect(1px, 1px, 1px, 1px)',
  clipPath: 'inset(50%)',
  whiteSpace: 'nowrap'
};

export const AnnouncerContext = React.createContext();

export const AnnouncerProvider = ({ children }) => {
  const [politeMessage, setPoliteMessage] = useState("");
  const [assertiveMessage, setAssertiveMessage] = useState("");
  const timeoutRef = useRef(null);

  // Clear message after announcement to allow re-announcing the same message
  const clearMessage = useCallback((setter) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setter("");
    }, 1000);
  }, []);

  const announce = useCallback((message, priority = 'polite') => {
    if (!message || typeof message !== 'string') return;

    if (priority === 'assertive') {
      setAssertiveMessage(message);
      clearMessage(setAssertiveMessage);
    } else {
      setPoliteMessage(message);
      clearMessage(setPoliteMessage);
    }
  }, [clearMessage]);

  const announcePolite = useCallback((message) => {
    announce(message, 'polite');
  }, [announce]);

  const announceAssertive = useCallback((message) => {
    announce(message, 'assertive');
  }, [announce]);

  const contextValue = {
    announce,
    announcePolite,
    announceAssertive
  };

  return (
    <AnnouncerContext.Provider value={contextValue}>
      {/* Polite announcements - don't interrupt current speech */}
      <div aria-live="polite" aria-atomic="true" style={announcerStyle}>
        {politeMessage}
      </div>

      {/* Assertive announcements - interrupt current speech for urgent messages */}
      <div aria-live="assertive" aria-atomic="true" style={announcerStyle}>
        {assertiveMessage}
      </div>

      {children}
    </AnnouncerContext.Provider>
  );
};

// Custom hook for easy access to announcer
export const useAnnouncer = () => {
  const context = useContext(AnnouncerContext);

  if (!context) {
    console.warn('useAnnouncer must be used within an AnnouncerProvider');
    // Return no-op functions if not in provider
    return {
      announce: () => {},
      announcePolite: () => {},
      announceAssertive: () => {}
    };
  }

  return context;
};