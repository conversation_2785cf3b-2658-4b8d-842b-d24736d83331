// src/components/AnnouncerDemo.jsx
import React, { useState } from 'react';
import { useAnnouncer } from './Announcer';

export default function AnnouncerDemo() {
  const [counter, setCounter] = useState(0);
  const [message, setMessage] = useState('');
  const { announcePolite, announceAssertive } = useAnnouncer();

  const handlePoliteAnnouncement = () => {
    const msg = message || `<PERSON><PERSON> clicked ${counter + 1} times`;
    announcePolite(msg);
    setCounter(prev => prev + 1);
  };

  const handleAssertiveAnnouncement = () => {
    const msg = message || 'This is an urgent announcement!';
    announceAssertive(msg);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    announcePolite('Form submitted successfully');
  };

  const handleError = () => {
    announceAssertive('Error: Something went wrong!');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 transition-colors duration-300">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-gray-100">
          Announcer Demo - Screen Reader Accessibility
        </h2>
        
        <div className="space-y-6">
          {/* Explanation */}
          <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              What is the Announcer?
            </h3>
            <p className="text-blue-800 dark:text-blue-200 text-sm">
              The Announcer component provides accessibility announcements for screen readers. 
              It uses ARIA live regions to communicate dynamic content changes to visually impaired users.
              The announcements are invisible but will be read aloud by screen readers.
            </p>
          </div>

          {/* Custom Message Input */}
          <div>
            <label htmlFor="custom-message" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
              Custom Message (optional):
            </label>
            <input
              id="custom-message"
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter a custom message to announce..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Demo Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100">Polite Announcements</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                These don't interrupt current screen reader speech
              </p>
              
              <button
                onClick={handlePoliteAnnouncement}
                className="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition"
              >
                🔊 Polite Announcement (Counter: {counter})
              </button>

              <button
                onClick={() => announcePolite('Page content has been updated')}
                className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition"
              >
                📄 Content Updated
              </button>

              <button
                onClick={() => announcePolite('New post has been added to the feed')}
                className="w-full px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded transition"
              >
                ➕ New Content Added
              </button>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900 dark:text-gray-100">Assertive Announcements</h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                These interrupt current speech for urgent messages
              </p>
              
              <button
                onClick={handleAssertiveAnnouncement}
                className="w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded transition"
              >
                ⚠️ Urgent Announcement
              </button>

              <button
                onClick={handleError}
                className="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition"
              >
                🚨 Error Announcement
              </button>

              <button
                onClick={() => announceAssertive('Form validation failed. Please check your inputs.')}
                className="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition"
              >
                ❌ Validation Error
              </button>
            </div>
          </div>

          {/* Form Demo */}
          <div className="border-t pt-6">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-4">Form Interaction Demo</h4>
            <form onSubmit={handleFormSubmit} className="space-y-4">
              <div>
                <label htmlFor="demo-input" className="block text-sm font-medium mb-1 text-gray-700 dark:text-gray-300">
                  Demo Input:
                </label>
                <input
                  id="demo-input"
                  type="text"
                  placeholder="Type something..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <button
                type="submit"
                className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded transition"
              >
                Submit Form (Announces Success)
              </button>
            </form>
          </div>

          {/* Usage Instructions */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">How to Test:</h4>
            <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
              <li>• Turn on a screen reader (NVDA, JAWS, VoiceOver, etc.)</li>
              <li>• Click the buttons above</li>
              <li>• Listen for the announcements</li>
              <li>• Polite announcements wait for current speech to finish</li>
              <li>• Assertive announcements interrupt current speech</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
